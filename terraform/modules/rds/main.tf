# Random password for database
resource "random_password" "database_password" {
  length  = 32
  special = false  # No special characters to avoid URL encoding issues
  upper   = true
  lower   = true
  numeric = true
}

# Parameter Store for database password
resource "aws_ssm_parameter" "database_password" {
  name  = "/convertecom/${var.environment}/database-password"
  type  = "SecureString"
  value = random_password.database_password.result

  tags = var.tags
}

# Parameter Store for database URL
resource "aws_ssm_parameter" "database_url" {
  name  = "/convertecom/${var.environment}/database-url"
  type  = "SecureString"
  value = "postgresql://${var.database_username}:${random_password.database_password.result}@${aws_db_instance.main.endpoint}/${var.database_name}?sslmode=require"

  tags = var.tags

  depends_on = [aws_db_instance.main]
}

# DB Parameter Group
resource "aws_db_parameter_group" "main" {
  family = "postgres15"
  name   = "${var.name_prefix}-db-params"

  parameter {
    name  = "log_statement"
    value = "all"
  }

  parameter {
    name  = "log_min_duration_statement"
    value = "1000"  # Log queries taking more than 1 second
  }

  parameter {
    name  = "shared_preload_libraries"
    value = "pg_stat_statements"
  }

  tags = var.tags

  lifecycle {
    create_before_destroy = true
  }
}

# DB Subnet Group (using public subnets for public access)
resource "aws_db_subnet_group" "main" {
  name       = "${var.name_prefix}-db-subnet-group-public"
  subnet_ids = var.public_subnet_ids

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-db-subnet-group-public"
  })
}

# RDS Instance
resource "aws_db_instance" "main" {
  identifier = "${var.name_prefix}-database"

  # Engine
  engine         = "postgres"
  engine_version = "15.8"
  instance_class = var.instance_class

  # Database
  db_name  = var.database_name
  username = var.database_username
  password = random_password.database_password.result

  # Storage
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.allocated_storage * 2  # Auto-scaling up to 2x
  storage_type          = "gp2"
  storage_encrypted     = true

  # Networking
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = var.allowed_security_group_ids
  publicly_accessible    = true

  # Backup
  backup_retention_period = var.backup_retention_period
  backup_window          = "03:00-04:00"  # UTC
  maintenance_window     = "sun:04:00-sun:05:00"  # UTC

  # Monitoring
  monitoring_interval = 0  # Disable enhanced monitoring for cost
  enabled_cloudwatch_logs_exports = ["postgresql"]

  # Parameter group
  parameter_group_name = aws_db_parameter_group.main.name

  # Multi-AZ
  multi_az = var.multi_az

  # Deletion protection (temporarily disabled for subnet group migration)
  deletion_protection = false
  skip_final_snapshot = var.environment != "prod"
  final_snapshot_identifier = var.environment == "prod" ? "${var.name_prefix}-final-snapshot-${formatdate("YYYY-MM-DD-hhmm", timestamp())}" : null

  # Performance Insights (disabled for cost optimization)
  performance_insights_enabled = false

  # Auto minor version upgrade
  auto_minor_version_upgrade = true

  # Apply changes immediately for non-prod
  apply_immediately = var.environment != "prod"

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-database"
  })

  lifecycle {
    ignore_changes = [
      password,  # Managed by Parameter Store
      final_snapshot_identifier
    ]
    replace_triggered_by = [
      aws_db_subnet_group.main
    ]
  }
}

# CloudWatch Log Group for PostgreSQL logs
resource "aws_cloudwatch_log_group" "postgresql" {
  name              = "/aws/rds/instance/${aws_db_instance.main.identifier}/postgresql"
  retention_in_days = var.log_retention_days

  tags = var.tags
}

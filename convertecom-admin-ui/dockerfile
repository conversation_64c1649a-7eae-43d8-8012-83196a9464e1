# Use the official Node.js image as the base image
FROM node:18

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and yarn.lock files to the container
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install

# Copy the rest of the application code to the container
COPY . .

# Build the React application
RUN yarn build

# Expose the port the app runs on (production server uses 8080)
EXPOSE 8080

# Command to run the application in production mode
CMD ["yarn", "start:prod"]


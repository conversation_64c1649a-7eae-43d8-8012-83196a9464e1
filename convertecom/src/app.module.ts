import { config } from 'dotenv';
import { Module } from '@nestjs/common';
import { CouponModule } from './coupon/coupon.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { parse } from 'pg-connection-string';
import { TenantModule } from './tenant/tenant.module';
import { WebhookModule } from './webhook/webhook.module';
import { OrderModule } from './order/order.module';
import { ApiSyncQueueModule } from './api-sync-queue/api-sync-queue.module';
import { OptinModule } from './optin/optin.module';
import { OauthModule } from './oauth/oauth.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { IntegrationModule } from './integration/integration.module';
config();

let connectionConfig;

if (process.env.DATABASE_URL) {
  const connection = parse(process.env.DATABASE_URL);

  connectionConfig = {
    type: process.env.TYPEORM_CONNECTION,
    host: connection.host,
    port: connection.port,
    username: connection.user,
    password: connection.password,
    database: connection.database,
    entities: [process.env.TYPEORM_ENTITIES],
    synchronize: false,
    extra: {
      /*
       rejectUnauthorized explicitly needed here because of updates in node 12.22.5, see node changelog for deets
       https://github.com/nodejs/node/blob/master/doc/changelogs/CHANGELOG_V12.md#2021-08-11-version-12225-erbium-lts-bethgriggs
       */
      ssl:
        typeof process.env.TYPEORM_SSL === 'undefined' || process.env.TYPEORM_SSL === 'true'
          ? {  rejectUnauthorized: false }
          : false,
    },
  };
}

@Module({
  imports: [
    TypeOrmModule.forRoot(connectionConfig),
    ApiSyncQueueModule,
    CouponModule,
    TenantModule,
    WebhookModule,
    OrderModule,
    OptinModule,
    OauthModule,
    IntegrationModule,
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'client'),
    }),
  ],
})
export class AppModule {}

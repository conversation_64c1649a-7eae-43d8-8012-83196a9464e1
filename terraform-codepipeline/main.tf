# CodePipeline Infrastructure for ConvertEcom Services
# This is a standalone Terraform configuration that doesn't depend on existing state

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data sources to get existing infrastructure
data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

# Get existing VPC by name
data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = ["${var.project_name}-${var.environment}-vpc"]
  }
}

# Get existing subnets
data "aws_subnets" "public" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.main.id]
  }
  filter {
    name   = "tag:Type"
    values = ["public"]
  }
}

# Get existing ECS cluster
data "aws_ecs_cluster" "main" {
  cluster_name = "${var.project_name}-${var.environment}-cluster"
}

# Get existing ECR repositories
data "aws_ecr_repository" "services" {
  for_each = var.services
  name     = each.value.repository_name
}

# Get existing ECS services
data "aws_ecs_service" "services" {
  for_each     = var.services
  service_name = "${var.project_name}-${var.environment}-${each.key}"
  cluster_arn  = data.aws_ecs_cluster.main.arn
}

# Local values
locals {
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name

  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
    Component   = "codepipeline"
  }

  # Inline buildspec configurations for each service
  buildspecs = {
    convertecom = yamlencode({
      version = "0.2"
      phases = {
        pre_build = {
          commands = [
            "echo Logging in to Amazon ECR...",
            "aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com",
            "REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME",
            "COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)",
            "IMAGE_TAG=$${COMMIT_HASH:=latest}",
            "echo Build started on `date`"
          ]
        }
        build = {
          commands = [
            "echo Build started on `date`",
            "echo Building the Docker image for convertecom service...",
            "cd $SERVICE_PATH",
            "docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f ../dockerfile .",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest"
          ]
        }
        post_build = {
          commands = [
            "echo Build completed on `date`",
            "echo Pushing the Docker images...",
            "docker push $REPOSITORY_URI:$IMAGE_TAG",
            "docker push $REPOSITORY_URI:latest",
            "echo Writing image definitions file...",
            "cd $CODEBUILD_SRC_DIR",
            "printf '[{\"name\":\"convertecom\",\"imageUri\":\"%s\"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json",
            "cat imagedefinitions.json"
          ]
        }
      }
      artifacts = {
        files = ["imagedefinitions.json"]
        name = "convertecom-build-$(date +%Y-%m-%d)"
      }
    })

    admin-api = yamlencode({
      version = "0.2"
      phases = {
        pre_build = {
          commands = [
            "echo Logging in to Amazon ECR...",
            "aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com",
            "REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME",
            "COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)",
            "IMAGE_TAG=$${COMMIT_HASH:=latest}",
            "echo Build started on `date`"
          ]
        }
        build = {
          commands = [
            "echo Build started on `date`",
            "echo Building the Docker image for admin-api service...",
            "cd $SERVICE_PATH",
            "docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest"
          ]
        }
        post_build = {
          commands = [
            "echo Build completed on `date`",
            "echo Pushing the Docker images...",
            "docker push $REPOSITORY_URI:$IMAGE_TAG",
            "docker push $REPOSITORY_URI:latest",
            "echo Writing image definitions file...",
            "cd $CODEBUILD_SRC_DIR",
            "printf '[{\"name\":\"admin-api\",\"imageUri\":\"%s\"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json",
            "cat imagedefinitions.json"
          ]
        }
      }
      artifacts = {
        files = ["imagedefinitions.json"]
        name = "admin-api-build-$(date +%Y-%m-%d)"
      }
    })

    admin-ui = yamlencode({
      version = "0.2"
      phases = {
        pre_build = {
          commands = [
            "echo Logging in to Amazon ECR...",
            "aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com",
            "REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME",
            "COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)",
            "IMAGE_TAG=$${COMMIT_HASH:=latest}",
            "echo Build started on `date`"
          ]
        }
        build = {
          commands = [
            "echo Build started on `date`",
            "echo Building the Docker image for admin-ui service...",
            "cd $SERVICE_PATH",
            "docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f dockerfile .",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest"
          ]
        }
        post_build = {
          commands = [
            "echo Build completed on `date`",
            "echo Pushing the Docker images...",
            "docker push $REPOSITORY_URI:$IMAGE_TAG",
            "docker push $REPOSITORY_URI:latest",
            "echo Writing image definitions file...",
            "cd $CODEBUILD_SRC_DIR",
            "printf '[{\"name\":\"admin-ui\",\"imageUri\":\"%s\"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json",
            "cat imagedefinitions.json"
          ]
        }
      }
      artifacts = {
        files = ["imagedefinitions.json"]
        name = "admin-ui-build-$(date +%Y-%m-%d)"
      }
    })

    queue = yamlencode({
      version = "0.2"
      phases = {
        pre_build = {
          commands = [
            "echo Logging in to Amazon ECR...",
            "aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com",
            "REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME",
            "COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)",
            "IMAGE_TAG=$${COMMIT_HASH:=latest}",
            "echo Build started on `date`"
          ]
        }
        build = {
          commands = [
            "echo Build started on `date`",
            "echo Building the Docker image for queue service...",
            "cd $SERVICE_PATH",
            "docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest"
          ]
        }
        post_build = {
          commands = [
            "echo Build completed on `date`",
            "echo Pushing the Docker images...",
            "docker push $REPOSITORY_URI:$IMAGE_TAG",
            "docker push $REPOSITORY_URI:latest",
            "echo Writing image definitions file...",
            "cd $CODEBUILD_SRC_DIR",
            "printf '[{\"name\":\"queue\",\"imageUri\":\"%s\"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json",
            "cat imagedefinitions.json"
          ]
        }
      }
      artifacts = {
        files = ["imagedefinitions.json"]
        name = "queue-build-$(date +%Y-%m-%d)"
      }
    })

    billing = yamlencode({
      version = "0.2"
      phases = {
        pre_build = {
          commands = [
            "echo Logging in to Amazon ECR...",
            "aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com",
            "REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME",
            "COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)",
            "IMAGE_TAG=$${COMMIT_HASH:=latest}",
            "echo Build started on `date`"
          ]
        }
        build = {
          commands = [
            "echo Build started on `date`",
            "echo Building the Docker image for billing service...",
            "cd $SERVICE_PATH",
            "docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG",
            "docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest"
          ]
        }
        post_build = {
          commands = [
            "echo Build completed on `date`",
            "echo Pushing the Docker images...",
            "docker push $REPOSITORY_URI:$IMAGE_TAG",
            "docker push $REPOSITORY_URI:latest",
            "echo Writing image definitions file...",
            "cd $CODEBUILD_SRC_DIR",
            "printf '[{\"name\":\"billing\",\"imageUri\":\"%s\"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json",
            "cat imagedefinitions.json"
          ]
        }
      }
      artifacts = {
        files = ["imagedefinitions.json"]
        name = "billing-build-$(date +%Y-%m-%d)"
      }
    })
  }
}

# S3 bucket for CodePipeline artifacts
resource "aws_s3_bucket" "codepipeline_artifacts" {
  bucket = "${var.project_name}-${var.environment}-codepipeline-artifacts-${random_string.bucket_suffix.result}"
  
  tags = merge(local.common_tags, {
    Name = "${var.project_name}-${var.environment}-codepipeline-artifacts"
  })
}

resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

resource "aws_s3_bucket_versioning" "codepipeline_artifacts" {
  bucket = aws_s3_bucket.codepipeline_artifacts.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "codepipeline_artifacts" {
  bucket = aws_s3_bucket.codepipeline_artifacts.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "codepipeline_artifacts" {
  bucket = aws_s3_bucket.codepipeline_artifacts.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# IAM role for CodePipeline
resource "aws_iam_role" "codepipeline" {
  name = "${var.project_name}-${var.environment}-codepipeline-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

resource "aws_iam_role_policy" "codepipeline" {
  name = "${var.project_name}-${var.environment}-codepipeline-policy"
  role = aws_iam_role.codepipeline.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetBucketVersioning",
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:PutObject"
        ]
        Resource = [
          aws_s3_bucket.codepipeline_artifacts.arn,
          "${aws_s3_bucket.codepipeline_artifacts.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "codebuild:BatchGetBuilds",
          "codebuild:StartBuild"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ecs:DescribeServices",
          "ecs:DescribeTaskDefinition",
          "ecs:DescribeTasks",
          "ecs:ListTasks",
          "ecs:RegisterTaskDefinition",
          "ecs:UpdateService"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = "*"
        Condition = {
          StringEqualsIfExists = {
            "iam:PassedToService" = [
              "ecs-tasks.amazonaws.com"
            ]
          }
        }
      }
    ]
  })
}

# IAM role for CodeBuild
resource "aws_iam_role" "codebuild" {
  name = "${var.project_name}-${var.environment}-codebuild-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

resource "aws_iam_role_policy" "codebuild" {
  name = "${var.project_name}-${var.environment}-codebuild-policy"
  role = aws_iam_role.codebuild.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${local.region}:${local.account_id}:log-group:/aws/codebuild/*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetBucketVersioning",
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:PutObject"
        ]
        Resource = [
          aws_s3_bucket.codepipeline_artifacts.arn,
          "${aws_s3_bucket.codepipeline_artifacts.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:GetAuthorizationToken",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ]
        Resource = "*"
      }
    ]
  })
}

# CodeBuild projects for each service
resource "aws_codebuild_project" "services" {
  for_each      = var.services
  name          = "${var.project_name}-${var.environment}-${each.key}-build"
  description   = "Build project for ${each.key} service"
  service_role  = aws_iam_role.codebuild.arn

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = var.build_compute_type
    image                      = var.build_image
    type                       = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode            = true

    environment_variable {
      name  = "AWS_DEFAULT_REGION"
      value = local.region
    }

    environment_variable {
      name  = "AWS_ACCOUNT_ID"
      value = local.account_id
    }

    environment_variable {
      name  = "IMAGE_REPO_NAME"
      value = data.aws_ecr_repository.services[each.key].name
    }

    environment_variable {
      name  = "IMAGE_TAG"
      value = "latest"
    }

    environment_variable {
      name  = "SERVICE_PATH"
      value = each.value.path
    }

    environment_variable {
      name  = "DOCKERFILE_PATH"
      value = each.value.dockerfile
    }
  }

  source {
    type = "CODEPIPELINE"
    buildspec = local.buildspecs[each.key]
  }

  tags = merge(local.common_tags, {
    Name    = "${var.project_name}-${var.environment}-${each.key}-build"
    Service = each.key
  })
}

# CodePipeline for each service
resource "aws_codepipeline" "services" {
  for_each = var.services
  name     = "${var.project_name}-${var.environment}-${each.key}-pipeline"
  role_arn = aws_iam_role.codepipeline.arn

  artifact_store {
    location = aws_s3_bucket.codepipeline_artifacts.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "ThirdParty"
      provider         = "GitHub"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        Owner      = var.github_owner
        Repo       = var.github_repo
        Branch     = var.github_branch
        OAuthToken = var.github_token
        PollForSourceChanges = false
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "Build"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      input_artifacts  = ["source_output"]
      output_artifacts = ["build_output"]
      version          = "1"

      configuration = {
        ProjectName = aws_codebuild_project.services[each.key].name
      }
    }
  }

  stage {
    name = "Deploy"

    action {
      name            = "Deploy"
      category        = "Deploy"
      owner           = "AWS"
      provider        = "ECS"
      input_artifacts = ["build_output"]
      version         = "1"

      configuration = {
        ClusterName = data.aws_ecs_cluster.main.cluster_name
        ServiceName = data.aws_ecs_service.services[each.key].service_name
        FileName    = "imagedefinitions.json"
      }
    }
  }

  tags = merge(local.common_tags, {
    Name    = "${var.project_name}-${var.environment}-${each.key}-pipeline"
    Service = each.key
  })
}

# Note: GitHub webhooks are not supported with GitHub v1 provider
# Pipelines will need to be triggered manually or upgrade to GitHub v2 with CodeStar Connections
#
# To trigger pipelines manually:
# aws codepipeline start-pipeline-execution --name <pipeline-name>

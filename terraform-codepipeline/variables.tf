variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "convertecom"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "prod"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "github_token" {
  description = "GitHub personal access token"
  type        = string
  sensitive   = true
}

variable "github_owner" {
  description = "GitHub repository owner"
  type        = string
  default     = "ConvertEcom"
}

variable "github_repo" {
  description = "GitHub repository name"
  type        = string
  default     = "convertecom-shopify"
}

variable "github_branch" {
  description = "GitHub branch to trigger pipeline"
  type        = string
  default     = "main"
}

variable "services" {
  description = "Map of services with their configurations"
  type = map(object({
    path           = string
    dockerfile     = string
    container_port = number
  }))
  default = {
    convertecom = {
      path           = "convertecom/"
      dockerfile     = "convertecom/dockerfile"
      container_port = 3000
    }
    admin-api = {
      path           = "convertecom-admin-master/"
      dockerfile     = "convertecom-admin-master/Dockerfile"
      container_port = 3000
    }
    admin-ui = {
      path           = "convertecom-admin-ui/"
      dockerfile     = "convertecom-admin-ui/dockerfile"
      container_port = 8080
    }
    queue = {
      path           = "convertecom-queue-master/"
      dockerfile     = "convertecom-queue-master/Dockerfile"
      container_port = 8000
    }
    billing = {
      path           = "convertecom-billing-service-master/"
      dockerfile     = "convertecom-billing-service-master/Dockerfile"
      container_port = 3000
    }
  }
}

variable "build_timeout" {
  description = "Build timeout in minutes"
  type        = number
  default     = 20
}

variable "build_compute_type" {
  description = "CodeBuild compute type"
  type        = string
  default     = "BUILD_GENERAL1_SMALL"
}

variable "build_image" {
  description = "CodeBuild Docker image"
  type        = string
  default     = "aws/codebuild/standard:7.0"
}

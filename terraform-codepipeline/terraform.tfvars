# Copy this file to terraform.tfvars and fill in your values

# Project configuration
project_name = "convertecom"
environment  = "prod"
aws_region   = "us-east-1"

# GitHub configuration
github_token  = "****************************************"
github_owner  = "ConvertEcom"
github_repo   = "convertecom-shopify"
github_branch = "main"

# Build configuration (optional - defaults are provided)
# build_timeout      = 20
# build_compute_type = "BUILD_GENERAL1_SMALL"
# build_image        = "aws/codebuild/standard:7.0"

# Services configuration (optional - defaults are provided)
# services = {
#   convertecom = {
#     path            = "convertecom/"
#     dockerfile      = "convertecom/dockerfile"
#     container_port  = 3000
#     repository_name = "convertecom"
#   }
#   admin-api = {
#     path            = "convertecom-admin-master/"
#     dockerfile      = "convertecom-admin-master/Dockerfile"
#     container_port  = 3000
#     repository_name = "convertecom-admin-master"
#   }
#   admin-ui = {
#     path            = "convertecom-admin-ui/"
#     dockerfile      = "convertecom-admin-ui/dockerfile"
#     container_port  = 8080
#     repository_name = "convertecom-admin-ui"
#   }
#   queue = {
#     path            = "convertecom-queue-master/"
#     dockerfile      = "convertecom-queue-master/Dockerfile"
#     container_port  = 8000
#     repository_name = "convertecom-queue-master"
#   }
#   billing = {
#     path            = "convertecom-billing-service-master/"
#     dockerfile      = "convertecom-billing-service-master/Dockerfile"
#     container_port  = 3000
#     repository_name = "convertecom-billing-service-master"
#   }
# }

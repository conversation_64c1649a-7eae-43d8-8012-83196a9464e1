{"version": 4, "terraform_version": "1.11.4", "serial": 41, "lineage": "96d0225e-5f2c-f8f8-4661-9f774f54f4f6", "outputs": {"codebuild_project_arns": {"value": {"admin-api": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-api-build", "admin-ui": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-ui-build", "billing": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-billing-build", "convertecom": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-convertecom-build", "queue": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-queue-build"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "codebuild_project_names": {"value": {"admin-api": "convertecom-prod-admin-api-build", "admin-ui": "convertecom-prod-admin-ui-build", "billing": "convertecom-prod-billing-build", "convertecom": "convertecom-prod-convertecom-build", "queue": "convertecom-prod-queue-build"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "codepipeline_arns": {"value": {"admin-api": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-admin-api-pipeline", "admin-ui": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-admin-ui-pipeline", "billing": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-billing-pipeline", "convertecom": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-convertecom-pipeline", "queue": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-queue-pipeline"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "codepipeline_names": {"value": {"admin-api": "convertecom-prod-admin-api-pipeline", "admin-ui": "convertecom-prod-admin-ui-pipeline", "billing": "convertecom-prod-billing-pipeline", "convertecom": "convertecom-prod-convertecom-pipeline", "queue": "convertecom-prod-queue-pipeline"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}, "iam_codebuild_role_arn": {"value": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "type": "string"}, "iam_codepipeline_role_arn": {"value": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "type": "string"}, "s3_artifacts_bucket": {"value": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "type": "string"}, "webhook_urls": {"value": {"admin-api": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiRmhXemRhbjFoVnJoQ1pvaG1GaXkyZjFJanI3N3E3V1J6ZlUzQWFEWVJhZFBXWDZrUWVpeGtoZzhzOWVPUXN3QjNIdTlXSXFjWmZkQ01PaWRuS0NzVkRxQU1oY3JWYnpGS3dYMHNRam9ZU2VYa0R4dHgydUdncXg5a1VVV3Zic0lVenRncFJmazNCZCtTSldUYlVUT3RobkNXL25PcVlmNDhucHYwR3BkOVkvSTJBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiIyVElQTnZQam0xVVJMZFdPIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1", "admin-ui": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoibzQ5M2w4VXhIRldiVUdOanJrSFNGWnZxRUFNZVdQSGlwWHhMQjRxTHorZHQyblZCMDIzTy9Ta1RmUjdUakFSelZaOVd0MjdCeEMyd1d3Zm1hR3V0Qk1MVlFOV0NqMkVnY1kxaFFpSkNBQUNWeDRybHBBY0J4R3luL3V3clkybDdIc3FEdHRHb2JDYTdYM1ZVY3g4MHhtY0dhZ2hLTFZSY0l2MVJBdWMyaEx2b1RnPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJPdzNyZUlyL3pKS1lCYUpDIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1", "billing": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiOEhYVVZmRUp6b1pIckx4TTduMHlYUC9mNjdEVDRCdDlPNU9VNGV0V2txc3YvdWg1aXVLdEc1SkRNLzJLcGhuVnBIUzE3Si9IdkZYemd5OVNvVFk3VmxaN2RMQnJxRnQ2U0IxejlTb3EwTUdmVnI1d1BmbTZxaERpaGZCWlR2RDlsUmtXdEQ5NC9pS2RQUDVKeHVkTU9IeFZtR3ZXTnlQaGlRQ2E1T2RWWXJ2RFZBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJuK0dBUU42MTlvaG9DTGFKIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1", "convertecom": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiay9zNmdzMXF6blVTOXRGZUx1WWluaE5CUE9iZmFDTk5oYTVCZTJGQ1EvUnlyMmE1VEhIa3R6dlNBRE52emxaRVA5QmR5U0VnejI0U3VYdndlc1Z3K1I0aFJxRnl2MkU0ZFJwT05ncmRpZHQ3b1VUZ25BT2lhV2xyU1BIZlRWeWRWZ2VQVnJiNDlCaWh4L1AxKzM2VUFOczhGMHgyYlNqZkRobTZpTHVHeVM0NWlBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiI3Q2x3MDU4VytZM2hRNDdHIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1", "queue": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiaU42WHF5RHNMcXBRMWpwVWE4UzhCYWdTRDRzelFmc2N5RVVYSnB4QjlhMFArcVg5NzNUNE4rMlJXM0ppdUU5RW55aHM3YkNvWGtRcHVZL05EK0RoUjFNaXRjb2o0TnNyL3p2dmxMZGVwYzdTMUV0V2N6bFdXUWR5Rm1hRlR3STlKUHZSdlBIb0pETUV5NVJpdkVRTGRSM1NNMnFVU0h5aEFpUDVTdEdyamM1Zm5nPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJTZUYvdHNReEwrbEFoOVNNIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "type": ["object", {"admin-api": "string", "admin-ui": "string", "billing": "string", "convertecom": "string", "queue": "string"}]}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/mathi-convertecom", "id": "************", "user_id": "AIDAYH2GE3MOCDWBALLYS"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_ecr_repository", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/convertecom-admin-master", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "id": "convertecom-admin-master", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "most_recent_image_tags": [], "name": "convertecom-admin-master", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/convertecom-admin-master", "tags": {"Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-admin-master", "Project": "convertecom", "Service": "convertecom-admin-master"}}, "sensitive_attributes": []}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/convertecom-admin-ui", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "id": "convertecom-admin-ui", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "most_recent_image_tags": [], "name": "convertecom-admin-ui", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/convertecom-admin-ui", "tags": {"Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-admin-ui", "Project": "convertecom", "Service": "convertecom-admin-ui"}}, "sensitive_attributes": []}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/convertecom-billing-service-master", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "id": "convertecom-billing-service-master", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "most_recent_image_tags": [], "name": "convertecom-billing-service-master", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/convertecom-billing-service-master", "tags": {"Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-billing-service-master", "Project": "convertecom", "Service": "convertecom-billing-service-master"}}, "sensitive_attributes": []}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/convertecom", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "id": "convertecom", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "most_recent_image_tags": [], "name": "convertecom", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/convertecom", "tags": {"Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom", "Project": "convertecom", "Service": "convertecom"}}, "sensitive_attributes": []}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-1:************:repository/convertecom-queue-master", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "id": "convertecom-queue-master", "image_scanning_configuration": [{"scan_on_push": true}], "image_tag_mutability": "MUTABLE", "most_recent_image_tags": [], "name": "convertecom-queue-master", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-1.amazonaws.com/convertecom-queue-master", "tags": {"Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-queue-master", "Project": "convertecom", "Service": "convertecom-queue-master"}}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_ecs_cluster", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "cluster_name": "convertecom-prod-cluster", "id": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "pending_tasks_count": 0, "registered_container_instances_count": 0, "running_tasks_count": 0, "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "disabled"}], "status": "ACTIVE", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom"}}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_ecs_service", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-admin-api", "availability_zone_rebalancing": "DISABLED", "cluster_arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "desired_count": 1, "id": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-admin-api", "launch_type": "FARGATE", "scheduling_strategy": "REPLICA", "service_name": "convertecom-prod-admin-api", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-master"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/convertecom-prod-admin-api:1"}, "sensitive_attributes": []}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-admin-ui", "availability_zone_rebalancing": "DISABLED", "cluster_arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "desired_count": 1, "id": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-admin-ui", "launch_type": "FARGATE", "scheduling_strategy": "REPLICA", "service_name": "convertecom-prod-admin-ui", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-admin-ui"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/convertecom-prod-admin-ui:1"}, "sensitive_attributes": []}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-billing", "availability_zone_rebalancing": "DISABLED", "cluster_arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "desired_count": 1, "id": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-billing", "launch_type": "FARGATE", "scheduling_strategy": "REPLICA", "service_name": "convertecom-prod-billing", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-billing-service-master"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/convertecom-prod-billing:1"}, "sensitive_attributes": []}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-convertecom", "availability_zone_rebalancing": "DISABLED", "cluster_arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "desired_count": 1, "id": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-convertecom", "launch_type": "FARGATE", "scheduling_strategy": "REPLICA", "service_name": "convertecom-prod-convertecom", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/convertecom-prod-convertecom:1"}, "sensitive_attributes": []}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-queue", "availability_zone_rebalancing": "DISABLED", "cluster_arn": "arn:aws:ecs:us-east-1:************:cluster/convertecom-prod-cluster", "desired_count": 1, "id": "arn:aws:ecs:us-east-1:************:service/convertecom-prod-cluster/convertecom-prod-queue", "launch_type": "FARGATE", "scheduling_strategy": "REPLICA", "service_name": "convertecom-prod-queue", "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Project": "convertecom", "Service": "convertecom-queue-master"}, "task_definition": "arn:aws:ecs:us-east-1:************:task-definition/convertecom-prod-queue:1"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_subnets", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"filter": [{"name": "tag:Type", "values": ["public"]}, {"name": "vpc-id", "values": ["vpc-0e8ea51886de5a37f"]}], "id": "us-east-1", "ids": [], "tags": null, "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-0e8ea51886de5a37f", "cidr_block": "********/16", "cidr_block_associations": [{"association_id": "vpc-cidr-assoc-0dea130b789c12ee0", "cidr_block": "********/16", "state": "associated"}], "default": false, "dhcp_options_id": "dopt-0adff13db89d33696", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "filter": [{"name": "tag:Name", "values": ["convertecom-prod-vpc"]}], "id": "vpc-0e8ea51886de5a37f", "instance_tenancy": "default", "ipv6_association_id": "", "ipv6_cidr_block": "", "main_route_table_id": "rtb-0487f0c66e0fd2f9b", "owner_id": "************", "state": null, "tags": {"Environment": "prod", "ManagedBy": "Terraform", "Name": "convertecom-prod-vpc", "Project": "convertecom"}, "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_codebuild_project", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-api-build", "artifacts": [{"artifact_identifier": "", "bucket_owner_access": "", "encryption_disabled": false, "location": "", "name": "convertecom-prod-admin-api-build", "namespace_type": "", "override_artifact_name": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}], "badge_enabled": false, "badge_url": "", "build_batch_config": [], "build_timeout": 60, "cache": [{"location": "", "modes": [], "type": "NO_CACHE"}], "concurrent_build_limit": 0, "description": "Build project for admin-api service", "encryption_key": "arn:aws:kms:us-east-1:************:alias/aws/s3", "environment": [{"certificate": "", "compute_type": "BUILD_GENERAL1_SMALL", "environment_variable": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "type": "PLAINTEXT", "value": "************"}, {"name": "IMAGE_REPO_NAME", "type": "PLAINTEXT", "value": "convertecom-admin-master"}, {"name": "IMAGE_TAG", "type": "PLAINTEXT", "value": "latest"}, {"name": "SERVICE_PATH", "type": "PLAINTEXT", "value": "convertecom-admin-master/"}, {"name": "DOCKERFILE_PATH", "type": "PLAINTEXT", "value": "convertecom-admin-master/Dockerfile"}], "fleet": [], "image": "aws/codebuild/standard:7.0", "image_pull_credentials_type": "CODEBUILD", "privileged_mode": true, "registry_credential": [], "type": "LINUX_CONTAINER"}], "file_system_locations": [], "id": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-api-build", "logs_config": [{"cloudwatch_logs": [{"group_name": "", "status": "ENABLED", "stream_name": ""}], "s3_logs": [{"bucket_owner_access": "", "encryption_disabled": false, "location": "", "status": "DISABLED"}]}], "name": "convertecom-prod-admin-api-build", "project_visibility": "PRIVATE", "public_project_alias": "", "queued_timeout": 480, "resource_access_role": "", "secondary_artifacts": [], "secondary_source_version": [], "secondary_sources": [], "service_role": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "source": [{"auth": [], "build_status_config": [], "buildspec": "\"artifacts\":\n  \"files\":\n  - \"imagedefinitions.json\"\n  \"name\": \"admin-api-build-$(date +%Y-%m-%d)\"\n\"phases\":\n  \"build\":\n    \"commands\":\n    - \"echo Build started on `date`\"\n    - \"echo Building the Docker image for admin-api service...\"\n    - \"cd $SERVICE_PATH\"\n    - \"docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest\"\n  \"post_build\":\n    \"commands\":\n    - \"echo Build completed on `date`\"\n    - \"echo Pushing the Docker images...\"\n    - \"docker push $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker push $REPOSITORY_URI:latest\"\n    - \"echo Writing image definitions file...\"\n    - \"printf '[{\\\"name\\\":\\\"admin-api\\\",\\\"imageUri\\\":\\\"%s\\\"}]' $REPOSITORY_URI:$IMAGE_TAG\n      > imagedefinitions.json\"\n    - \"cat imagedefinitions.json\"\n  \"pre_build\":\n    \"commands\":\n    - \"echo Logging in to Amazon ECR...\"\n    - \"aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username\n      AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com\"\n    - \"REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME\"\n    - \"COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)\"\n    - \"IMAGE_TAG=${COMMIT_HASH:=latest}\"\n    - \"echo Build started on `date`\"\n\"version\": \"0.2\"\n", "git_clone_depth": 0, "git_submodules_config": [], "insecure_ssl": false, "location": "", "report_build_status": false, "type": "CODEPIPELINE"}], "source_version": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-build", "Project": "convertecom", "Service": "admin-api"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-build", "Project": "convertecom", "Service": "admin-api"}, "vpc_config": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_region.current"]}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-ui-build", "artifacts": [{"artifact_identifier": "", "bucket_owner_access": "", "encryption_disabled": false, "location": "", "name": "convertecom-prod-admin-ui-build", "namespace_type": "", "override_artifact_name": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}], "badge_enabled": false, "badge_url": "", "build_batch_config": [], "build_timeout": 60, "cache": [{"location": "", "modes": [], "type": "NO_CACHE"}], "concurrent_build_limit": 0, "description": "Build project for admin-ui service", "encryption_key": "arn:aws:kms:us-east-1:************:alias/aws/s3", "environment": [{"certificate": "", "compute_type": "BUILD_GENERAL1_SMALL", "environment_variable": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "type": "PLAINTEXT", "value": "************"}, {"name": "IMAGE_REPO_NAME", "type": "PLAINTEXT", "value": "convertecom-admin-ui"}, {"name": "IMAGE_TAG", "type": "PLAINTEXT", "value": "latest"}, {"name": "SERVICE_PATH", "type": "PLAINTEXT", "value": "convertecom-admin-ui/"}, {"name": "DOCKERFILE_PATH", "type": "PLAINTEXT", "value": "convertecom-admin-ui/dockerfile"}], "fleet": [], "image": "aws/codebuild/standard:7.0", "image_pull_credentials_type": "CODEBUILD", "privileged_mode": true, "registry_credential": [], "type": "LINUX_CONTAINER"}], "file_system_locations": [], "id": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-admin-ui-build", "logs_config": [{"cloudwatch_logs": [{"group_name": "", "status": "ENABLED", "stream_name": ""}], "s3_logs": [{"bucket_owner_access": "", "encryption_disabled": false, "location": "", "status": "DISABLED"}]}], "name": "convertecom-prod-admin-ui-build", "project_visibility": "PRIVATE", "public_project_alias": "", "queued_timeout": 480, "resource_access_role": "", "secondary_artifacts": [], "secondary_source_version": [], "secondary_sources": [], "service_role": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "source": [{"auth": [], "build_status_config": [], "buildspec": "\"artifacts\":\n  \"files\":\n  - \"imagedefinitions.json\"\n  \"name\": \"admin-ui-build-$(date +%Y-%m-%d)\"\n\"phases\":\n  \"build\":\n    \"commands\":\n    - \"echo Build started on `date`\"\n    - \"echo Building the Docker image for admin-ui service...\"\n    - \"cd $SERVICE_PATH\"\n    - \"docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f dockerfile .\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest\"\n  \"post_build\":\n    \"commands\":\n    - \"echo Build completed on `date`\"\n    - \"echo Pushing the Docker images...\"\n    - \"docker push $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker push $REPOSITORY_URI:latest\"\n    - \"echo Writing image definitions file...\"\n    - \"printf '[{\\\"name\\\":\\\"admin-ui\\\",\\\"imageUri\\\":\\\"%s\\\"}]' $REPOSITORY_URI:$IMAGE_TAG\n      > imagedefinitions.json\"\n    - \"cat imagedefinitions.json\"\n  \"pre_build\":\n    \"commands\":\n    - \"echo Logging in to Amazon ECR...\"\n    - \"aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username\n      AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com\"\n    - \"REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME\"\n    - \"COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)\"\n    - \"IMAGE_TAG=${COMMIT_HASH:=latest}\"\n    - \"echo Build started on `date`\"\n\"version\": \"0.2\"\n", "git_clone_depth": 0, "git_submodules_config": [], "insecure_ssl": false, "location": "", "report_build_status": false, "type": "CODEPIPELINE"}], "source_version": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-build", "Project": "convertecom", "Service": "admin-ui"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-build", "Project": "convertecom", "Service": "admin-ui"}, "vpc_config": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_region.current"]}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-billing-build", "artifacts": [{"artifact_identifier": "", "bucket_owner_access": "", "encryption_disabled": false, "location": "", "name": "convertecom-prod-billing-build", "namespace_type": "", "override_artifact_name": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}], "badge_enabled": false, "badge_url": "", "build_batch_config": [], "build_timeout": 60, "cache": [{"location": "", "modes": [], "type": "NO_CACHE"}], "concurrent_build_limit": 0, "description": "Build project for billing service", "encryption_key": "arn:aws:kms:us-east-1:************:alias/aws/s3", "environment": [{"certificate": "", "compute_type": "BUILD_GENERAL1_SMALL", "environment_variable": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "type": "PLAINTEXT", "value": "************"}, {"name": "IMAGE_REPO_NAME", "type": "PLAINTEXT", "value": "convertecom-billing-service-master"}, {"name": "IMAGE_TAG", "type": "PLAINTEXT", "value": "latest"}, {"name": "SERVICE_PATH", "type": "PLAINTEXT", "value": "convertecom-billing-service-master/"}, {"name": "DOCKERFILE_PATH", "type": "PLAINTEXT", "value": "convertecom-billing-service-master/Dockerfile"}], "fleet": [], "image": "aws/codebuild/standard:7.0", "image_pull_credentials_type": "CODEBUILD", "privileged_mode": true, "registry_credential": [], "type": "LINUX_CONTAINER"}], "file_system_locations": [], "id": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-billing-build", "logs_config": [{"cloudwatch_logs": [{"group_name": "", "status": "ENABLED", "stream_name": ""}], "s3_logs": [{"bucket_owner_access": "", "encryption_disabled": false, "location": "", "status": "DISABLED"}]}], "name": "convertecom-prod-billing-build", "project_visibility": "PRIVATE", "public_project_alias": "", "queued_timeout": 480, "resource_access_role": "", "secondary_artifacts": [], "secondary_source_version": [], "secondary_sources": [], "service_role": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "source": [{"auth": [], "build_status_config": [], "buildspec": "\"artifacts\":\n  \"files\":\n  - \"imagedefinitions.json\"\n  \"name\": \"billing-build-$(date +%Y-%m-%d)\"\n\"phases\":\n  \"build\":\n    \"commands\":\n    - \"echo Build started on `date`\"\n    - \"echo Building the Docker image for billing service...\"\n    - \"cd $SERVICE_PATH\"\n    - \"docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest\"\n  \"post_build\":\n    \"commands\":\n    - \"echo Build completed on `date`\"\n    - \"echo Pushing the Docker images...\"\n    - \"docker push $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker push $REPOSITORY_URI:latest\"\n    - \"echo Writing image definitions file...\"\n    - \"printf '[{\\\"name\\\":\\\"billing\\\",\\\"imageUri\\\":\\\"%s\\\"}]' $REPOSITORY_URI:$IMAGE_TAG\n      > imagedefinitions.json\"\n    - \"cat imagedefinitions.json\"\n  \"pre_build\":\n    \"commands\":\n    - \"echo Logging in to Amazon ECR...\"\n    - \"aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username\n      AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com\"\n    - \"REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME\"\n    - \"COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)\"\n    - \"IMAGE_TAG=${COMMIT_HASH:=latest}\"\n    - \"echo Build started on `date`\"\n\"version\": \"0.2\"\n", "git_clone_depth": 0, "git_submodules_config": [], "insecure_ssl": false, "location": "", "report_build_status": false, "type": "CODEPIPELINE"}], "source_version": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-build", "Project": "convertecom", "Service": "billing"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-build", "Project": "convertecom", "Service": "billing"}, "vpc_config": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_region.current"]}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-convertecom-build", "artifacts": [{"artifact_identifier": "", "bucket_owner_access": "", "encryption_disabled": false, "location": "", "name": "convertecom-prod-convertecom-build", "namespace_type": "", "override_artifact_name": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}], "badge_enabled": false, "badge_url": "", "build_batch_config": [], "build_timeout": 60, "cache": [{"location": "", "modes": [], "type": "NO_CACHE"}], "concurrent_build_limit": 0, "description": "Build project for convertecom service", "encryption_key": "arn:aws:kms:us-east-1:************:alias/aws/s3", "environment": [{"certificate": "", "compute_type": "BUILD_GENERAL1_SMALL", "environment_variable": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "type": "PLAINTEXT", "value": "************"}, {"name": "IMAGE_REPO_NAME", "type": "PLAINTEXT", "value": "convertecom"}, {"name": "IMAGE_TAG", "type": "PLAINTEXT", "value": "latest"}, {"name": "SERVICE_PATH", "type": "PLAINTEXT", "value": "convertecom/"}, {"name": "DOCKERFILE_PATH", "type": "PLAINTEXT", "value": "convertecom/dockerfile"}], "fleet": [], "image": "aws/codebuild/standard:7.0", "image_pull_credentials_type": "CODEBUILD", "privileged_mode": true, "registry_credential": [], "type": "LINUX_CONTAINER"}], "file_system_locations": [], "id": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-convertecom-build", "logs_config": [{"cloudwatch_logs": [{"group_name": "", "status": "ENABLED", "stream_name": ""}], "s3_logs": [{"bucket_owner_access": "", "encryption_disabled": false, "location": "", "status": "DISABLED"}]}], "name": "convertecom-prod-convertecom-build", "project_visibility": "PRIVATE", "public_project_alias": "", "queued_timeout": 480, "resource_access_role": "", "secondary_artifacts": [], "secondary_source_version": [], "secondary_sources": [], "service_role": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "source": [{"auth": [], "build_status_config": [], "buildspec": "\"artifacts\":\n  \"files\":\n  - \"imagedefinitions.json\"\n  \"name\": \"convertecom-build-$(date +%Y-%m-%d)\"\n\"phases\":\n  \"build\":\n    \"commands\":\n    - \"echo Build started on `date`\"\n    - \"echo Building the Docker image for convertecom service...\"\n    - \"cd $SERVICE_PATH\"\n    - \"docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f ../dockerfile .\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest\"\n  \"post_build\":\n    \"commands\":\n    - \"echo Build completed on `date`\"\n    - \"echo Pushing the Docker images...\"\n    - \"docker push $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker push $REPOSITORY_URI:latest\"\n    - \"echo Writing image definitions file...\"\n    - \"printf '[{\\\"name\\\":\\\"convertecom\\\",\\\"imageUri\\\":\\\"%s\\\"}]' $REPOSITORY_URI:$IMAGE_TAG\n      > imagedefinitions.json\"\n    - \"cat imagedefinitions.json\"\n  \"pre_build\":\n    \"commands\":\n    - \"echo Logging in to Amazon ECR...\"\n    - \"aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username\n      AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com\"\n    - \"REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME\"\n    - \"COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)\"\n    - \"IMAGE_TAG=${COMMIT_HASH:=latest}\"\n    - \"echo Build started on `date`\"\n\"version\": \"0.2\"\n", "git_clone_depth": 0, "git_submodules_config": [], "insecure_ssl": false, "location": "", "report_build_status": false, "type": "CODEPIPELINE"}], "source_version": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-build", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-build", "Project": "convertecom", "Service": "convertecom"}, "vpc_config": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_region.current"]}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-queue-build", "artifacts": [{"artifact_identifier": "", "bucket_owner_access": "", "encryption_disabled": false, "location": "", "name": "convertecom-prod-queue-build", "namespace_type": "", "override_artifact_name": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}], "badge_enabled": false, "badge_url": "", "build_batch_config": [], "build_timeout": 60, "cache": [{"location": "", "modes": [], "type": "NO_CACHE"}], "concurrent_build_limit": 0, "description": "Build project for queue service", "encryption_key": "arn:aws:kms:us-east-1:************:alias/aws/s3", "environment": [{"certificate": "", "compute_type": "BUILD_GENERAL1_SMALL", "environment_variable": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "type": "PLAINTEXT", "value": "************"}, {"name": "IMAGE_REPO_NAME", "type": "PLAINTEXT", "value": "convertecom-queue-master"}, {"name": "IMAGE_TAG", "type": "PLAINTEXT", "value": "latest"}, {"name": "SERVICE_PATH", "type": "PLAINTEXT", "value": "convertecom-queue-master/"}, {"name": "DOCKERFILE_PATH", "type": "PLAINTEXT", "value": "convertecom-queue-master/Dockerfile"}], "fleet": [], "image": "aws/codebuild/standard:7.0", "image_pull_credentials_type": "CODEBUILD", "privileged_mode": true, "registry_credential": [], "type": "LINUX_CONTAINER"}], "file_system_locations": [], "id": "arn:aws:codebuild:us-east-1:************:project/convertecom-prod-queue-build", "logs_config": [{"cloudwatch_logs": [{"group_name": "", "status": "ENABLED", "stream_name": ""}], "s3_logs": [{"bucket_owner_access": "", "encryption_disabled": false, "location": "", "status": "DISABLED"}]}], "name": "convertecom-prod-queue-build", "project_visibility": "PRIVATE", "public_project_alias": "", "queued_timeout": 480, "resource_access_role": "", "secondary_artifacts": [], "secondary_source_version": [], "secondary_sources": [], "service_role": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "source": [{"auth": [], "build_status_config": [], "buildspec": "\"artifacts\":\n  \"files\":\n  - \"imagedefinitions.json\"\n  \"name\": \"queue-build-$(date +%Y-%m-%d)\"\n\"phases\":\n  \"build\":\n    \"commands\":\n    - \"echo Build started on `date`\"\n    - \"echo Building the Docker image for queue service...\"\n    - \"cd $SERVICE_PATH\"\n    - \"docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG -f Dockerfile .\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URI:latest\"\n  \"post_build\":\n    \"commands\":\n    - \"echo Build completed on `date`\"\n    - \"echo Pushing the Docker images...\"\n    - \"docker push $REPOSITORY_URI:$IMAGE_TAG\"\n    - \"docker push $REPOSITORY_URI:latest\"\n    - \"echo Writing image definitions file...\"\n    - \"printf '[{\\\"name\\\":\\\"queue\\\",\\\"imageUri\\\":\\\"%s\\\"}]' $REPOSITORY_URI:$IMAGE_TAG\n      > imagedefinitions.json\"\n    - \"cat imagedefinitions.json\"\n  \"pre_build\":\n    \"commands\":\n    - \"echo Logging in to Amazon ECR...\"\n    - \"aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username\n      AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com\"\n    - \"REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME\"\n    - \"COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)\"\n    - \"IMAGE_TAG=${COMMIT_HASH:=latest}\"\n    - \"echo Build started on `date`\"\n\"version\": \"0.2\"\n", "git_clone_depth": 0, "git_submodules_config": [], "insecure_ssl": false, "location": "", "report_build_status": false, "type": "CODEPIPELINE"}], "source_version": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-build", "Project": "convertecom", "Service": "queue"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-build", "Project": "convertecom", "Service": "queue"}, "vpc_config": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_codepipeline", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-admin-api-pipeline", "artifact_store": [{"encryption_key": [], "location": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "region": "", "type": "S3"}], "execution_mode": "SUPERSEDED", "id": "convertecom-prod-admin-api-pipeline", "name": "convertecom-prod-admin-api-pipeline", "pipeline_type": "V1", "role_arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "stage": [{"action": [{"category": "Source", "configuration": {"Branch": "main", "OAuthToken": "****************************************", "Owner": "ConvertEcom", "PollForSourceChanges": "false", "Repo": "convertecom-shopify"}, "input_artifacts": [], "name": "Source", "namespace": "", "output_artifacts": ["source_output"], "owner": "ThirdParty", "provider": "GitHub", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Source", "on_failure": [], "on_success": []}, {"action": [{"category": "Build", "configuration": {"ProjectName": "convertecom-prod-admin-api-build"}, "input_artifacts": ["source_output"], "name": "Build", "namespace": "", "output_artifacts": ["build_output"], "owner": "AWS", "provider": "CodeBuild", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Build", "on_failure": [], "on_success": []}, {"action": [{"category": "Deploy", "configuration": {"ClusterName": "convertecom-prod-cluster", "FileName": "imagedefinitions.json", "ServiceName": "convertecom-prod-admin-api"}, "input_artifacts": ["build_output"], "name": "Deploy", "namespace": "", "output_artifacts": [], "owner": "AWS", "provider": "ECS", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Deploy", "on_failure": [], "on_success": []}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-pipeline", "Project": "convertecom", "Service": "admin-api"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-pipeline", "Project": "convertecom", "Service": "admin-api"}, "trigger": [], "trigger_all": [], "variable": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "stage"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "action"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "configuration"}, {"type": "index", "value": {"value": "OAuthToken", "type": "string"}}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-admin-ui-pipeline", "artifact_store": [{"encryption_key": [], "location": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "region": "", "type": "S3"}], "execution_mode": "SUPERSEDED", "id": "convertecom-prod-admin-ui-pipeline", "name": "convertecom-prod-admin-ui-pipeline", "pipeline_type": "V1", "role_arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "stage": [{"action": [{"category": "Source", "configuration": {"Branch": "main", "OAuthToken": "****************************************", "Owner": "ConvertEcom", "PollForSourceChanges": "false", "Repo": "convertecom-shopify"}, "input_artifacts": [], "name": "Source", "namespace": "", "output_artifacts": ["source_output"], "owner": "ThirdParty", "provider": "GitHub", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Source", "on_failure": [], "on_success": []}, {"action": [{"category": "Build", "configuration": {"ProjectName": "convertecom-prod-admin-ui-build"}, "input_artifacts": ["source_output"], "name": "Build", "namespace": "", "output_artifacts": ["build_output"], "owner": "AWS", "provider": "CodeBuild", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Build", "on_failure": [], "on_success": []}, {"action": [{"category": "Deploy", "configuration": {"ClusterName": "convertecom-prod-cluster", "FileName": "imagedefinitions.json", "ServiceName": "convertecom-prod-admin-ui"}, "input_artifacts": ["build_output"], "name": "Deploy", "namespace": "", "output_artifacts": [], "owner": "AWS", "provider": "ECS", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Deploy", "on_failure": [], "on_success": []}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-pipeline", "Project": "convertecom", "Service": "admin-ui"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-pipeline", "Project": "convertecom", "Service": "admin-ui"}, "trigger": [], "trigger_all": [], "variable": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "stage"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "action"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "configuration"}, {"type": "index", "value": {"value": "OAuthToken", "type": "string"}}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-billing-pipeline", "artifact_store": [{"encryption_key": [], "location": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "region": "", "type": "S3"}], "execution_mode": "SUPERSEDED", "id": "convertecom-prod-billing-pipeline", "name": "convertecom-prod-billing-pipeline", "pipeline_type": "V1", "role_arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "stage": [{"action": [{"category": "Source", "configuration": {"Branch": "main", "OAuthToken": "****************************************", "Owner": "ConvertEcom", "PollForSourceChanges": "false", "Repo": "convertecom-shopify"}, "input_artifacts": [], "name": "Source", "namespace": "", "output_artifacts": ["source_output"], "owner": "ThirdParty", "provider": "GitHub", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Source", "on_failure": [], "on_success": []}, {"action": [{"category": "Build", "configuration": {"ProjectName": "convertecom-prod-billing-build"}, "input_artifacts": ["source_output"], "name": "Build", "namespace": "", "output_artifacts": ["build_output"], "owner": "AWS", "provider": "CodeBuild", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Build", "on_failure": [], "on_success": []}, {"action": [{"category": "Deploy", "configuration": {"ClusterName": "convertecom-prod-cluster", "FileName": "imagedefinitions.json", "ServiceName": "convertecom-prod-billing"}, "input_artifacts": ["build_output"], "name": "Deploy", "namespace": "", "output_artifacts": [], "owner": "AWS", "provider": "ECS", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Deploy", "on_failure": [], "on_success": []}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-pipeline", "Project": "convertecom", "Service": "billing"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-pipeline", "Project": "convertecom", "Service": "billing"}, "trigger": [], "trigger_all": [], "variable": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "stage"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "action"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "configuration"}, {"type": "index", "value": {"value": "OAuthToken", "type": "string"}}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-convertecom-pipeline", "artifact_store": [{"encryption_key": [], "location": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "region": "", "type": "S3"}], "execution_mode": "SUPERSEDED", "id": "convertecom-prod-convertecom-pipeline", "name": "convertecom-prod-convertecom-pipeline", "pipeline_type": "V1", "role_arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "stage": [{"action": [{"category": "Source", "configuration": {"Branch": "main", "OAuthToken": "****************************************", "Owner": "ConvertEcom", "PollForSourceChanges": "false", "Repo": "convertecom-shopify"}, "input_artifacts": [], "name": "Source", "namespace": "", "output_artifacts": ["source_output"], "owner": "ThirdParty", "provider": "GitHub", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Source", "on_failure": [], "on_success": []}, {"action": [{"category": "Build", "configuration": {"ProjectName": "convertecom-prod-convertecom-build"}, "input_artifacts": ["source_output"], "name": "Build", "namespace": "", "output_artifacts": ["build_output"], "owner": "AWS", "provider": "CodeBuild", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Build", "on_failure": [], "on_success": []}, {"action": [{"category": "Deploy", "configuration": {"ClusterName": "convertecom-prod-cluster", "FileName": "imagedefinitions.json", "ServiceName": "convertecom-prod-convertecom"}, "input_artifacts": ["build_output"], "name": "Deploy", "namespace": "", "output_artifacts": [], "owner": "AWS", "provider": "ECS", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Deploy", "on_failure": [], "on_success": []}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-pipeline", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-pipeline", "Project": "convertecom", "Service": "convertecom"}, "trigger": [], "trigger_all": [], "variable": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "stage"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "action"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "configuration"}, {"type": "index", "value": {"value": "OAuthToken", "type": "string"}}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:convertecom-prod-queue-pipeline", "artifact_store": [{"encryption_key": [], "location": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "region": "", "type": "S3"}], "execution_mode": "SUPERSEDED", "id": "convertecom-prod-queue-pipeline", "name": "convertecom-prod-queue-pipeline", "pipeline_type": "V1", "role_arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "stage": [{"action": [{"category": "Source", "configuration": {"Branch": "main", "OAuthToken": "****************************************", "Owner": "ConvertEcom", "PollForSourceChanges": "false", "Repo": "convertecom-shopify"}, "input_artifacts": [], "name": "Source", "namespace": "", "output_artifacts": ["source_output"], "owner": "ThirdParty", "provider": "GitHub", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Source", "on_failure": [], "on_success": []}, {"action": [{"category": "Build", "configuration": {"ProjectName": "convertecom-prod-queue-build"}, "input_artifacts": ["source_output"], "name": "Build", "namespace": "", "output_artifacts": ["build_output"], "owner": "AWS", "provider": "CodeBuild", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Build", "on_failure": [], "on_success": []}, {"action": [{"category": "Deploy", "configuration": {"ClusterName": "convertecom-prod-cluster", "FileName": "imagedefinitions.json", "ServiceName": "convertecom-prod-queue"}, "input_artifacts": ["build_output"], "name": "Deploy", "namespace": "", "output_artifacts": [], "owner": "AWS", "provider": "ECS", "region": "", "role_arn": "", "run_order": 1, "timeout_in_minutes": 0, "version": "1"}], "before_entry": [], "name": "Deploy", "on_failure": [], "on_success": []}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-pipeline", "Project": "convertecom", "Service": "queue"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-pipeline", "Project": "convertecom", "Service": "queue"}, "trigger": [], "trigger_all": [], "variable": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "stage"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "action"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "configuration"}, {"type": "index", "value": {"value": "OAuthToken", "type": "string"}}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_codepipeline_webhook", "name": "services", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "admin-api", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-admin-api-webhook", "authentication": "GITHUB_HMAC", "authentication_configuration": [{"allowed_ip_range": "", "secret_token": "****"}], "filter": [{"json_path": "$.ref", "match_equals": "refs/heads/main"}], "id": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-admin-api-webhook", "name": "convertecom-prod-admin-api-webhook", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-webhook", "Project": "convertecom", "Service": "admin-api"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-api-webhook", "Project": "convertecom", "Service": "admin-api"}, "target_action": "Source", "target_pipeline": "convertecom-prod-admin-api-pipeline", "url": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiRmhXemRhbjFoVnJoQ1pvaG1GaXkyZjFJanI3N3E3V1J6ZlUzQWFEWVJhZFBXWDZrUWVpeGtoZzhzOWVPUXN3QjNIdTlXSXFjWmZkQ01PaWRuS0NzVkRxQU1oY3JWYnpGS3dYMHNRam9ZU2VYa0R4dHgydUdncXg5a1VVV3Zic0lVenRncFJmazNCZCtTSldUYlVUT3RobkNXL25PcVlmNDhucHYwR3BkOVkvSTJBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiIyVElQTnZQam0xVVJMZFdPIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "authentication_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "secret_token"}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_codepipeline.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "admin-ui", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-admin-ui-webhook", "authentication": "GITHUB_HMAC", "authentication_configuration": [{"allowed_ip_range": "", "secret_token": "****"}], "filter": [{"json_path": "$.ref", "match_equals": "refs/heads/main"}], "id": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-admin-ui-webhook", "name": "convertecom-prod-admin-ui-webhook", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-webhook", "Project": "convertecom", "Service": "admin-ui"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-admin-ui-webhook", "Project": "convertecom", "Service": "admin-ui"}, "target_action": "Source", "target_pipeline": "convertecom-prod-admin-ui-pipeline", "url": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoibzQ5M2w4VXhIRldiVUdOanJrSFNGWnZxRUFNZVdQSGlwWHhMQjRxTHorZHQyblZCMDIzTy9Ta1RmUjdUakFSelZaOVd0MjdCeEMyd1d3Zm1hR3V0Qk1MVlFOV0NqMkVnY1kxaFFpSkNBQUNWeDRybHBBY0J4R3luL3V3clkybDdIc3FEdHRHb2JDYTdYM1ZVY3g4MHhtY0dhZ2hLTFZSY0l2MVJBdWMyaEx2b1RnPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJPdzNyZUlyL3pKS1lCYUpDIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "authentication_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "secret_token"}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_codepipeline.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "billing", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-billing-webhook", "authentication": "GITHUB_HMAC", "authentication_configuration": [{"allowed_ip_range": "", "secret_token": "****"}], "filter": [{"json_path": "$.ref", "match_equals": "refs/heads/main"}], "id": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-billing-webhook", "name": "convertecom-prod-billing-webhook", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-webhook", "Project": "convertecom", "Service": "billing"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-billing-webhook", "Project": "convertecom", "Service": "billing"}, "target_action": "Source", "target_pipeline": "convertecom-prod-billing-pipeline", "url": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiOEhYVVZmRUp6b1pIckx4TTduMHlYUC9mNjdEVDRCdDlPNU9VNGV0V2txc3YvdWg1aXVLdEc1SkRNLzJLcGhuVnBIUzE3Si9IdkZYemd5OVNvVFk3VmxaN2RMQnJxRnQ2U0IxejlTb3EwTUdmVnI1d1BmbTZxaERpaGZCWlR2RDlsUmtXdEQ5NC9pS2RQUDVKeHVkTU9IeFZtR3ZXTnlQaGlRQ2E1T2RWWXJ2RFZBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJuK0dBUU42MTlvaG9DTGFKIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "authentication_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "secret_token"}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_codepipeline.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "convertecom", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-convertecom-webhook", "authentication": "GITHUB_HMAC", "authentication_configuration": [{"allowed_ip_range": "", "secret_token": "****"}], "filter": [{"json_path": "$.ref", "match_equals": "refs/heads/main"}], "id": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-convertecom-webhook", "name": "convertecom-prod-convertecom-webhook", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-webhook", "Project": "convertecom", "Service": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-convertecom-webhook", "Project": "convertecom", "Service": "convertecom"}, "target_action": "Source", "target_pipeline": "convertecom-prod-convertecom-pipeline", "url": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiay9zNmdzMXF6blVTOXRGZUx1WWluaE5CUE9iZmFDTk5oYTVCZTJGQ1EvUnlyMmE1VEhIa3R6dlNBRE52emxaRVA5QmR5U0VnejI0U3VYdndlc1Z3K1I0aFJxRnl2MkU0ZFJwT05ncmRpZHQ3b1VUZ25BT2lhV2xyU1BIZlRWeWRWZ2VQVnJiNDlCaWh4L1AxKzM2VUFOczhGMHgyYlNqZkRobTZpTHVHeVM0NWlBPT0iLCJpdlBhcmFtZXRlclNwZWMiOiI3Q2x3MDU4VytZM2hRNDdHIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "authentication_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "secret_token"}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_codepipeline.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}, {"index_key": "queue", "schema_version": 0, "attributes": {"arn": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-queue-webhook", "authentication": "GITHUB_HMAC", "authentication_configuration": [{"allowed_ip_range": "", "secret_token": "****"}], "filter": [{"json_path": "$.ref", "match_equals": "refs/heads/main"}], "id": "arn:aws:codepipeline:us-east-1:************:webhook:convertecom-prod-queue-webhook", "name": "convertecom-prod-queue-webhook", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-webhook", "Project": "convertecom", "Service": "queue"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-queue-webhook", "Project": "convertecom", "Service": "queue"}, "target_action": "Source", "target_pipeline": "convertecom-prod-queue-pipeline", "url": "https://us-east-1.webhooks.aws/trigger?t=eyJlbmNyeXB0ZWREYXRhIjoiaU42WHF5RHNMcXBRMWpwVWE4UzhCYWdTRDRzelFmc2N5RVVYSnB4QjlhMFArcVg5NzNUNE4rMlJXM0ppdUU5RW55aHM3YkNvWGtRcHVZL05EK0RoUjFNaXRjb2o0TnNyL3p2dmxMZGVwYzdTMUV0V2N6bFdXUWR5Rm1hRlR3STlKUHZSdlBIb0pETUV5NVJpdkVRTGRSM1NNMnFVU0h5aEFpUDVTdEdyamM1Zm5nPT0iLCJpdlBhcmFtZXRlclNwZWMiOiJTZUYvdHNReEwrbEFoOVNNIiwibWF0ZXJpYWxTZXRTZXJpYWwiOjF9&v=1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "authentication_configuration"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "secret_token"}]], "private": "bnVsbA==", "dependencies": ["aws_codebuild_project.services", "aws_codepipeline.services", "aws_iam_role.codebuild", "aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_ecr_repository.services", "data.aws_ecs_cluster.main", "data.aws_ecs_service.services", "data.aws_region.current", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "codebuild", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/convertecom-prod-codebuild-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"codebuild.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-03T07:18:14Z", "description": "", "force_detach_policies": false, "id": "convertecom-prod-codebuild-role", "inline_policy": [{"name": "convertecom-prod-codebuild-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:us-east-1:************:log-group:/aws/codebuild/*\"},{\"Action\":[\"s3:GetBucketVersioning\",\"s3:GetObject\",\"s3:GetObjectVersion\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi\",\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi/*\"]},{\"Action\":[\"ecr:BatchCheckLayerAvailability\",\"ecr:GetDownloadUrlForLayer\",\"ecr:BatchGetImage\",\"ecr:GetAuthorizationToken\",\"ecr:InitiateLayerUpload\",\"ecr:UploadLayerPart\",\"ecr:CompleteLayerUpload\",\"ecr:PutImage\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "convertecom-prod-codebuild-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Project": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Project": "convertecom"}, "unique_id": "AROAYH2GE3MOD3ADLK6WY"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "codepipeline", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/convertecom-prod-codepipeline-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"codepipeline.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-03T07:18:14Z", "description": "", "force_detach_policies": false, "id": "convertecom-prod-codepipeline-role", "inline_policy": [{"name": "convertecom-prod-codepipeline-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:GetBucketVersioning\",\"s3:GetObject\",\"s3:GetObjectVersion\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi\",\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi/*\"]},{\"Action\":[\"codebuild:BatchGetBuilds\",\"codebuild:StartBuild\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ecs:DescribeServices\",\"ecs:DescribeTaskDefinition\",\"ecs:DescribeTasks\",\"ecs:ListTasks\",\"ecs:RegisterTaskDefinition\",\"ecs:UpdateService\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"iam:PassRole\"],\"Condition\":{\"StringEqualsIfExists\":{\"iam:PassedToService\":[\"ecs-tasks.amazonaws.com\"]}},\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "convertecom-prod-codepipeline-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Project": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Project": "convertecom"}, "unique_id": "AROAYH2GE3MOASD4JLKAJ"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "codebuild", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "convertecom-prod-codebuild-role:convertecom-prod-codebuild-policy", "name": "convertecom-prod-codebuild-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:us-east-1:************:log-group:/aws/codebuild/*\"},{\"Action\":[\"s3:GetBucketVersioning\",\"s3:GetObject\",\"s3:GetObjectVersion\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi\",\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi/*\"]},{\"Action\":[\"ecr:BatchCheckLayerAvailability\",\"ecr:GetDownloadUrlForLayer\",\"ecr:BatchGetImage\",\"ecr:GetAuthorizationToken\",\"ecr:InitiateLayerUpload\",\"ecr:UploadLayerPart\",\"ecr:CompleteLayerUpload\",\"ecr:PutImage\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "convertecom-prod-codebuild-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codebuild", "aws_s3_bucket.codepipeline_artifacts", "data.aws_caller_identity.current", "data.aws_region.current", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "codepipeline", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "convertecom-prod-codepipeline-role:convertecom-prod-codepipeline-policy", "name": "convertecom-prod-codepipeline-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:GetBucketVersioning\",\"s3:GetObject\",\"s3:GetObjectVersion\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi\",\"arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi/*\"]},{\"Action\":[\"codebuild:BatchGetBuilds\",\"codebuild:StartBuild\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ecs:DescribeServices\",\"ecs:DescribeTaskDefinition\",\"ecs:DescribeTasks\",\"ecs:ListTasks\",\"ecs:RegisterTaskDefinition\",\"ecs:UpdateService\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"iam:PassRole\"],\"Condition\":{\"StringEqualsIfExists\":{\"iam:PassedToService\":[\"ecs-tasks.amazonaws.com\"]}},\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "convertecom-prod-codepipeline-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.codepipeline", "aws_s3_bucket.codepipeline_artifacts", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "codepipeline_artifacts", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::convertecom-prod-codepipeline-artifacts-lca2y5vi", "bucket": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "bucket_domain_name": "convertecom-prod-codepipeline-artifacts-lca2y5vi.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "convertecom-prod-codepipeline-artifacts-lca2y5vi.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "dd49548d263531bf3b9d240f37fd3e5e8bfada2cae5ab4f92bcb139a4b197c19", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-codepipeline-artifacts", "Project": "convertecom"}, "tags_all": {"Component": "codepipeline", "Environment": "prod", "ManagedBy": "terraform", "Name": "convertecom-prod-codepipeline-artifacts", "Project": "convertecom"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "codepipeline_artifacts", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "id": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.codepipeline_artifacts", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "codepipeline_artifacts", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "expected_bucket_owner": "", "id": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.codepipeline_artifacts", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "codepipeline_artifacts", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "expected_bucket_owner": "", "id": "convertecom-prod-codepipeline-artifacts-lca2y5vi", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.codepipeline_artifacts", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "random_string", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 2, "attributes": {"id": "lca2y5vi", "keepers": null, "length": 8, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "lca2y5vi", "special": false, "upper": false}, "sensitive_attributes": []}]}], "check_results": null}
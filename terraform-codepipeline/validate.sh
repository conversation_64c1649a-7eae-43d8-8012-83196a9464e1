#!/bin/bash

# ConvertEcom CodePipeline Validation Script
# This script validates the deployment and tests pipeline functionality

set -e

echo "🔍 ConvertEcom CodePipeline Validation"
echo "====================================="

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &>/dev/null; then
    echo "❌ AWS CLI not configured or no valid credentials"
    echo "📝 Please configure AWS CLI: aws configure"
    exit 1
fi

echo "✅ AWS CLI configured"

# Get AWS account and region
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
REGION=$(aws configure get region || echo "us-east-1")

echo "🏢 AWS Account: $ACCOUNT_ID"
echo "🌍 AWS Region: $REGION"

# Check if infrastructure exists
echo ""
echo "🔍 Checking existing infrastructure..."

# Check ECS cluster
if aws ecs describe-clusters --clusters convertecom-prod-cluster &>/dev/null; then
    echo "✅ ECS cluster found"
else
    echo "❌ ECS cluster not found (convertecom-prod-cluster)"
    echo "📝 Please deploy the main infrastructure first"
    exit 1
fi

# Check ECR repositories
echo "🔍 Checking ECR repositories..."
SERVICES=("convertecom" "admin-api" "admin-ui" "queue" "billing")
for service in "${SERVICES[@]}"; do
    if aws ecr describe-repositories --repository-names "convertecom-$service" &>/dev/null; then
        echo "✅ ECR repository found: convertecom-$service"
    else
        echo "❌ ECR repository not found: convertecom-$service"
        exit 1
    fi
done

# Check ECS services
echo "🔍 Checking ECS services..."
for service in "${SERVICES[@]}"; do
    if aws ecs describe-services --cluster convertecom-prod-cluster --services "convertecom-prod-$service" &>/dev/null; then
        echo "✅ ECS service found: convertecom-prod-$service"
    else
        echo "❌ ECS service not found: convertecom-prod-$service"
        exit 1
    fi
done

echo ""
echo "✅ All prerequisites validated successfully!"
echo ""
echo "🚀 Ready to deploy CodePipeline infrastructure"
echo "📝 Run: ./deploy.sh"

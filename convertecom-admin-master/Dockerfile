# Use the official Node.js image as the base image
FROM node:20

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json files to the container
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the application code to the container
COPY . .

# Build the TypeScript application
RUN npm run build

# Create tsconfig-paths-bootstrap.js for production
RUN echo "const tsConfigPaths = require('tsconfig-paths');" > tsconfig-paths-bootstrap.js && \
    echo "const baseUrl = './dist';" >> tsconfig-paths-bootstrap.js && \
    echo "const cleanup = tsConfigPaths.register({" >> tsconfig-paths-bootstrap.js && \
    echo "  baseUrl," >> tsconfig-paths-bootstrap.js && \
    echo "  paths: {" >> tsconfig-paths-bootstrap.js && \
    echo "    '@app/*': ['src/*']" >> tsconfig-paths-bootstrap.js && \
    echo "  }" >> tsconfig-paths-bootstrap.js && \
    echo "});" >> tsconfig-paths-bootstrap.js

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application in production mode
CMD ["npm", "run", "start:prod"]

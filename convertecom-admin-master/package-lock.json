{"name": "convertecom-admin-server", "version": "0.0.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "convertecom-admin-server", "version": "0.0.1", "license": "MIT", "dependencies": {"@honeycombio/opentelemetry-node": "^0.3.2", "@nestjs/common": "^6.10.11", "@nestjs/core": "^6.10.11", "@nestjs/jwt": "^6.0.0", "@nestjs/passport": "^6.0.0", "@nestjs/platform-express": "^6.10", "@nestjs/schedule": "1", "@nestjs/testing": "^6.0.0", "@nestjs/typeorm": "^6.3.4", "@opentelemetry/auto-instrumentations-node": "^0.36.3", "@sentry/node": "5.6.2", "aws-s3": "^2.0.5", "aws-sdk": "^2.576.0", "axios": "^0.19.0", "bcrypt": "^5.1.0", "cache-manager": "^3.4.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "dompurify": "^2.0.12", "dotenv": "^7.0.0", "helmet": "^3.18.0", "jest": "^23.6.0", "jsdom": "^16.2.2", "json2csv": "^4.5.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.11", "lossless-json": "^1.0.5", "mailchimp-api-v3": "^1.13.1", "moment": "^2.24.0", "moment-timezone": "^0.5.26", "nanoid": "^3.1.23", "nest-raven": "^5.0.0", "nodemon": "^1.18.9", "passport": "^0.4.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.0", "pg": "^8.9.0", "pg-connection-string": "^2.1.0", "prettier": "^1.15.3", "reflect-metadata": "^0.1.12", "request-ip": "^2.1.3", "rimraf": "^2.6.2", "rxjs": "^6.3.3", "ts-node": "^7.0.1", "tsconfig-paths": "^3.7.0", "typeorm": "^0.2.16"}, "devDependencies": {"@types/cache-manager": "^2.10.3", "@types/cron": "^1.7.3", "@types/dompurify": "^2.0.2", "@types/express": "^5.0.1", "@types/hapi__shot": "6.0.0", "@types/jest": "^23.3.13", "@types/json2csv": "^4.5.0", "@types/lossless-json": "^1.0.1", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.12", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/supertest": "^2.0.7", "supertest": "^3.4.1", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "tslint": "5.12.1", "typescript": "4.9"}, "engines": {"node": "20.x"}}, "node_modules/@apollo/federation": {"version": "0.12.1", "license": "SEE LICENSE IN LICENSE.md", "optional": true, "dependencies": {"apollo-graphql": "^0.4.0", "apollo-server-env": "^2.4.3", "core-js": "^3.4.0", "lodash.xorby": "^4.7.0"}, "engines": {"node": ">=8"}, "peerDependencies": {"graphql": "^14.0.2"}}, "node_modules/@apollo/federation/node_modules/apollo-server-env": {"version": "2.4.5", "license": "MIT", "optional": true, "dependencies": {"node-fetch": "^2.1.2", "util.promisify": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@apollo/gateway": {"version": "0.12.1", "license": "SEE LICENSE IN LICENSE.md", "optional": true, "dependencies": {"@apollo/federation": "^0.12.1", "@types/node-fetch": "2.5.4", "apollo-engine-reporting-protobuf": "^0.4.4", "apollo-env": "^0.6.1", "apollo-graphql": "^0.4.0", "apollo-server-caching": "^0.5.1", "apollo-server-core": "^2.10.1", "apollo-server-env": "^2.4.3", "apollo-server-types": "^0.2.10", "graphql-extensions": "^0.10.10", "loglevel": "^1.6.1", "loglevel-debug": "^0.0.1", "pretty-format": "^24.7.0"}, "engines": {"node": ">=8"}, "peerDependencies": {"graphql": "^14.2.1"}}, "node_modules/@apollo/gateway/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "optional": true, "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@apollo/gateway/node_modules/@types/node-fetch": {"version": "2.5.4", "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@apollo/gateway/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/@apollo/gateway/node_modules/apollo-server-caching": {"version": "0.5.3", "license": "MIT", "optional": true, "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@apollo/gateway/node_modules/apollo-server-env": {"version": "2.4.5", "license": "MIT", "optional": true, "dependencies": {"node-fetch": "^2.1.2", "util.promisify": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@apollo/gateway/node_modules/apollo-server-types": {"version": "0.2.10", "license": "MIT", "optional": true, "dependencies": {"apollo-engine-reporting-protobuf": "^0.4.4", "apollo-server-caching": "^0.5.1", "apollo-server-env": "^2.4.3"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0"}}, "node_modules/@apollo/gateway/node_modules/pretty-format": {"version": "24.9.0", "license": "MIT", "optional": true, "dependencies": {"@jest/types": "^24.9.0", "ansi-regex": "^4.0.0", "ansi-styles": "^3.2.0", "react-is": "^16.8.4"}, "engines": {"node": ">= 6"}}, "node_modules/@apollo/protobufjs": {"version": "1.2.2", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "@types/node": "^10.1.0", "long": "^4.0.0"}, "bin": {"apollo-pbjs": "bin/pbjs", "apollo-pbts": "bin/pbts"}}, "node_modules/@apollo/protobufjs/node_modules/@types/node": {"version": "10.17.60", "license": "MIT"}, "node_modules/@apollo/protobufjs/node_modules/long": {"version": "4.0.0", "license": "Apache-2.0"}, "node_modules/@apollographql/apollo-tools": {"version": "0.5.4", "license": "MIT", "engines": {"node": ">=8", "npm": ">=6"}, "peerDependencies": {"graphql": "^14.2.1 || ^15.0.0 || ^16.0.0"}}, "node_modules/@apollographql/graphql-playground-html": {"version": "1.6.27", "license": "MIT", "dependencies": {"xss": "^1.0.8"}}, "node_modules/@apollographql/graphql-upload-8-fork": {"version": "8.1.4", "license": "MIT", "dependencies": {"@types/express": "*", "@types/fs-capacitor": "^2.0.0", "@types/koa": "*", "busboy": "^0.3.1", "fs-capacitor": "^2.0.4", "http-errors": "^1.7.3", "object-path": "^0.11.4"}, "engines": {"node": ">=8.5"}, "peerDependencies": {"graphql": "0.13.1 - 15"}}, "node_modules/@apollographql/graphql-upload-8-fork/node_modules/busboy": {"version": "0.3.1", "dependencies": {"dicer": "0.3.0"}, "engines": {"node": ">=4.5.0"}}, "node_modules/@apollographql/graphql-upload-8-fork/node_modules/dicer": {"version": "0.3.0", "dependencies": {"streamsearch": "0.1.2"}, "engines": {"node": ">=4.5.0"}}, "node_modules/@apollographql/graphql-upload-8-fork/node_modules/http-errors": {"version": "1.8.1", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/@apollographql/graphql-upload-8-fork/node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/@apollographql/graphql-upload-8-fork/node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/@babel/code-frame": {"version": "7.26.2", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@dsherret/to-absolute-glob": {"version": "2.0.2", "license": "MIT", "optional": true, "dependencies": {"is-absolute": "^1.0.0", "is-negated-glob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@graphql-toolkit/common": {"version": "0.9.0", "license": "MIT", "dependencies": {"@kamilkisiela/graphql-tools": "4.0.6", "aggregate-error": "3.0.1", "lodash": "4.17.15"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0"}}, "node_modules/@graphql-toolkit/common/node_modules/lodash": {"version": "4.17.15", "license": "MIT"}, "node_modules/@graphql-toolkit/file-loading": {"version": "0.9.0", "license": "MIT", "dependencies": {"globby": "11.0.0", "unixify": "1.0.0"}, "peerDependencies": {"graphql": "^14.5.8"}}, "node_modules/@graphql-toolkit/schema-merging": {"version": "0.9.0", "license": "MIT", "dependencies": {"@graphql-toolkit/common": "0.9.0", "@kamilkisiela/graphql-tools": "4.0.6", "deepmerge": "4.2.2", "tslib": "1.10.0"}, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0"}}, "node_modules/@graphql-toolkit/schema-merging/node_modules/tslib": {"version": "1.10.0", "license": "Apache-2.0"}, "node_modules/@grpc/grpc-js": {"version": "1.13.3", "license": "Apache-2.0", "dependencies": {"@grpc/proto-loader": "^0.7.13", "@js-sdsl/ordered-map": "^4.4.2"}, "engines": {"node": ">=12.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.15", "license": "Apache-2.0", "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@grpc/proto-loader/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@grpc/proto-loader/node_modules/protobufjs": {"version": "7.5.0", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@grpc/proto-loader/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/@grpc/proto-loader/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@hapi/b64": {"version": "5.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "9.x.x"}}, "node_modules/@hapi/boom": {"version": "9.1.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "9.x.x"}}, "node_modules/@hapi/bourne": {"version": "2.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/cryptiles": {"version": "5.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/boom": "9.x.x"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@hapi/hoek": {"version": "9.3.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/iron": {"version": "6.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/b64": "5.x.x", "@hapi/boom": "9.x.x", "@hapi/bourne": "2.x.x", "@hapi/cryptiles": "5.x.x", "@hapi/hoek": "9.x.x"}}, "node_modules/@hapi/podium": {"version": "4.1.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "9.x.x", "@hapi/teamwork": "5.x.x", "@hapi/validate": "1.x.x"}}, "node_modules/@hapi/podium/node_modules/@hapi/validate": {"version": "1.1.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0"}}, "node_modules/@hapi/shot": {"version": "6.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/validate": "^2.0.1"}}, "node_modules/@hapi/shot/node_modules/@hapi/hoek": {"version": "11.0.7", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/shot/node_modules/@hapi/topo": {"version": "6.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^11.0.2"}}, "node_modules/@hapi/shot/node_modules/@hapi/validate": {"version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/topo": "^6.0.1"}}, "node_modules/@hapi/teamwork": {"version": "5.1.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=12.0.0"}}, "node_modules/@hapi/topo": {"version": "5.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@honeycombio/opentelemetry-node": {"version": "0.3.2", "license": "Apache-2.0", "dependencies": {"@grpc/grpc-js": "^1.7.3", "@opentelemetry/api": "^1.3.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.34.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.34.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.34.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.34.0", "@opentelemetry/resources": "^1.8.0", "@opentelemetry/sdk-metrics": "^1.8.0", "@opentelemetry/sdk-node": "^0.34.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "axios": "^1.1.3"}, "engines": {"node": ">=14"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/@opentelemetry/resources": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/@opentelemetry/sdk-metrics": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/resources": "1.30.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.10.0"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/@opentelemetry/sdk-trace-base": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/resources": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/axios": {"version": "1.8.4", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/@honeycombio/opentelemetry-node/node_modules/form-data": {"version": "4.0.2", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/@jest/types": {"version": "24.9.0", "license": "MIT", "optional": true, "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^1.1.1", "@types/yargs": "^13.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/@josephg/resolvable": {"version": "1.0.1", "license": "ISC"}, "node_modules/@js-sdsl/ordered-map": {"version": "4.4.2", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/@kamilkisiela/graphql-tools": {"version": "4.0.6", "license": "MIT", "dependencies": {"apollo-link": "^1.2.3", "apollo-utilities": "^1.0.1", "deprecated-decorator": "^0.1.6", "iterall": "^1.1.3", "uuid": "^3.1.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0"}}, "node_modules/@mapbox/node-pre-gyp": {"version": "1.0.11", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.11"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/chownr": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/fs-minipass": {"version": "2.1.0", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/minipass": {"version": "5.0.0", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/minizlib": {"version": "2.1.2", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/mkdirp": {"version": "1.0.4", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/tar": {"version": "6.2.1", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@nestjs/common": {"version": "6.11.11", "license": "MIT", "dependencies": {"axios": "0.19.2", "cli-color": "2.0.0", "tslib": "1.11.1", "uuid": "7.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"reflect-metadata": "^0.1.12", "rxjs": "^6.0.0"}}, "node_modules/@nestjs/common/node_modules/tslib": {"version": "1.11.1", "license": "Apache-2.0"}, "node_modules/@nestjs/common/node_modules/uuid": {"version": "7.0.1", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@nestjs/core": {"version": "6.11.11", "hasInstallScript": true, "license": "MIT", "dependencies": {"@nuxtjs/opencollective": "0.2.2", "fast-safe-stringify": "2.0.7", "iterare": "1.2.0", "object-hash": "2.0.3", "path-to-regexp": "3.2.0", "tslib": "1.11.1", "uuid": "7.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"@nestjs/common": "^6.0.0", "reflect-metadata": "^0.1.12", "rxjs": "^6.0.0"}}, "node_modules/@nestjs/core/node_modules/tslib": {"version": "1.11.1", "license": "Apache-2.0"}, "node_modules/@nestjs/core/node_modules/uuid": {"version": "7.0.1", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@nestjs/graphql": {"version": "6.6.2", "license": "MIT", "dependencies": {"chokidar": "3.3.1", "fast-glob": "3.2.2", "graphql-tools": "4.0.7", "iterall": "1.2.2", "lodash": "4.17.15", "merge-graphql-schemas": "1.7.6", "normalize-path": "3.0.0", "tslib": "1.11.1", "uuid": "7.0.1"}, "optionalDependencies": {"@apollo/federation": "^0.12.0", "@apollo/gateway": "^0.12.0", "ts-morph": "^5.0.0", "type-graphql": "^0.17.3"}, "peerDependencies": {"@nestjs/core": "^6.10.0", "graphql": "^14.1.1", "reflect-metadata": "^0.1.12"}}, "node_modules/@nestjs/graphql/node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/@nestjs/graphql/node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@nestjs/graphql/node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/@nestjs/graphql/node_modules/chokidar": {"version": "3.3.1", "license": "MIT", "dependencies": {"anymatch": "~3.1.1", "braces": "~3.0.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.3.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.1.2"}}, "node_modules/@nestjs/graphql/node_modules/fast-glob": {"version": "3.2.2", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.0", "merge2": "^1.3.0", "micromatch": "^4.0.2", "picomatch": "^2.2.1"}, "engines": {"node": ">=8"}}, "node_modules/@nestjs/graphql/node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@nestjs/graphql/node_modules/graphql-tools": {"version": "4.0.7", "license": "MIT", "dependencies": {"apollo-link": "^1.2.3", "apollo-utilities": "^1.0.1", "deprecated-decorator": "^0.1.6", "iterall": "^1.1.3", "uuid": "^3.1.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0"}}, "node_modules/@nestjs/graphql/node_modules/graphql-tools/node_modules/iterall": {"version": "1.3.0", "license": "MIT"}, "node_modules/@nestjs/graphql/node_modules/graphql-tools/node_modules/uuid": {"version": "3.4.0", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/@nestjs/graphql/node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@nestjs/graphql/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@nestjs/graphql/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@nestjs/graphql/node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/@nestjs/graphql/node_modules/iterall": {"version": "1.2.2", "license": "MIT"}, "node_modules/@nestjs/graphql/node_modules/lodash": {"version": "4.17.15", "license": "MIT"}, "node_modules/@nestjs/graphql/node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/@nestjs/graphql/node_modules/readdirp": {"version": "3.3.0", "license": "MIT", "dependencies": {"picomatch": "^2.0.7"}, "engines": {"node": ">=8.10.0"}}, "node_modules/@nestjs/graphql/node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/@nestjs/graphql/node_modules/tslib": {"version": "1.11.1", "license": "Apache-2.0"}, "node_modules/@nestjs/graphql/node_modules/uuid": {"version": "7.0.1", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@nestjs/jwt": {"version": "6.1.2", "license": "MIT", "dependencies": {"@types/jsonwebtoken": "8.3.7", "jsonwebtoken": "8.5.1"}, "peerDependencies": {"@nestjs/common": "^6.0.0"}}, "node_modules/@nestjs/passport": {"version": "6.2.0", "license": "MIT", "peerDependencies": {"@nestjs/common": "^6.0.0", "passport": "^0.4.0"}}, "node_modules/@nestjs/platform-express": {"version": "6.11.11", "license": "MIT", "dependencies": {"body-parser": "1.19.0", "cors": "2.8.5", "express": "4.17.1", "multer": "1.4.2", "tslib": "1.11.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"@nestjs/common": "^6.0.0", "@nestjs/core": "^6.0.0"}}, "node_modules/@nestjs/platform-express/node_modules/tslib": {"version": "1.11.1", "license": "Apache-2.0"}, "node_modules/@nestjs/schedule": {"version": "1.1.0", "license": "MIT", "dependencies": {"cron": "1.8.2", "uuid": "8.3.2"}, "peerDependencies": {"@nestjs/common": "^6.10.11 || ^7.0.0 || ^8.0.0", "@nestjs/core": "^7.0.0 || ^8.0.0", "reflect-metadata": "^0.1.12"}}, "node_modules/@nestjs/schedule/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@nestjs/testing": {"version": "6.11.11", "license": "MIT", "dependencies": {"optional": "0.1.4", "tslib": "1.11.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"@nestjs/common": "^6.0.0", "@nestjs/core": "^6.0.0"}}, "node_modules/@nestjs/testing/node_modules/tslib": {"version": "1.11.1", "license": "Apache-2.0"}, "node_modules/@nestjs/typeorm": {"version": "6.3.4", "license": "MIT", "dependencies": {"uuid": "^7.0.2"}, "peerDependencies": {"@nestjs/common": "^6.7.0", "@nestjs/core": "^6.7.0", "reflect-metadata": "^0.1.12", "rxjs": "^6.0.0", "typeorm": "^0.2.7"}}, "node_modules/@nestjs/typeorm/node_modules/uuid": {"version": "7.0.3", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@nuxtjs/opencollective": {"version": "0.2.2", "license": "MIT", "dependencies": {"chalk": "^2.4.1", "consola": "^2.3.0", "node-fetch": "^2.3.0"}, "bin": {"opencollective": "bin/opencollective.js"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}}, "node_modules/@opentelemetry/api": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@opentelemetry/api/-/api-1.3.0.tgz", "integrity": "sha512-YveTnGNsFFixTKJz09Oi4zYkiLT5af3WpZDu4aIUM7xX+2bHAkOJayFTVQd6zB8kkWPpbua4Ha6Ql00grdLlJQ==", "license": "Apache-2.0", "engines": {"node": ">=8.0.0"}}, "node_modules/@opentelemetry/auto-instrumentations-node": {"version": "0.36.6", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.38.0", "@opentelemetry/instrumentation-amqplib": "^0.32.3", "@opentelemetry/instrumentation-aws-lambda": "^0.35.1", "@opentelemetry/instrumentation-aws-sdk": "^0.34.1", "@opentelemetry/instrumentation-bunyan": "^0.31.2", "@opentelemetry/instrumentation-cassandra-driver": "^0.32.2", "@opentelemetry/instrumentation-connect": "^0.31.2", "@opentelemetry/instrumentation-dataloader": "^0.4.1", "@opentelemetry/instrumentation-dns": "^0.31.3", "@opentelemetry/instrumentation-express": "^0.32.2", "@opentelemetry/instrumentation-fastify": "^0.31.2", "@opentelemetry/instrumentation-fs": "^0.7.2", "@opentelemetry/instrumentation-generic-pool": "^0.31.2", "@opentelemetry/instrumentation-graphql": "^0.34.1", "@opentelemetry/instrumentation-grpc": "^0.38.0", "@opentelemetry/instrumentation-hapi": "^0.31.2", "@opentelemetry/instrumentation-http": "^0.38.0", "@opentelemetry/instrumentation-ioredis": "^0.34.1", "@opentelemetry/instrumentation-knex": "^0.31.2", "@opentelemetry/instrumentation-koa": "^0.34.4", "@opentelemetry/instrumentation-lru-memoizer": "^0.32.2", "@opentelemetry/instrumentation-memcached": "^0.31.2", "@opentelemetry/instrumentation-mongodb": "^0.34.2", "@opentelemetry/instrumentation-mongoose": "^0.32.2", "@opentelemetry/instrumentation-mysql": "^0.33.1", "@opentelemetry/instrumentation-mysql2": "^0.33.2", "@opentelemetry/instrumentation-nestjs-core": "^0.32.3", "@opentelemetry/instrumentation-net": "^0.31.2", "@opentelemetry/instrumentation-pg": "^0.35.1", "@opentelemetry/instrumentation-pino": "^0.33.2", "@opentelemetry/instrumentation-redis": "^0.34.5", "@opentelemetry/instrumentation-redis-4": "^0.34.4", "@opentelemetry/instrumentation-restify": "^0.32.2", "@opentelemetry/instrumentation-router": "^0.32.2", "@opentelemetry/instrumentation-socket.io": "^0.33.2", "@opentelemetry/instrumentation-tedious": "^0.5.2", "@opentelemetry/instrumentation-winston": "^0.31.2"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/auto-instrumentations-node/node_modules/@opentelemetry/instrumentation": {"version": "0.38.0", "license": "Apache-2.0", "dependencies": {"require-in-the-middle": "^6.0.0", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/auto-instrumentations-node/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@opentelemetry/auto-instrumentations-node/node_modules/require-in-the-middle": {"version": "6.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@opentelemetry/auto-instrumentations-node/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/context-async-hooks": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/core": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/exporter-jaeger": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0", "@opentelemetry/semantic-conventions": "1.8.0", "jaeger-client": "^3.15.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/exporter-jaeger/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/exporter-metrics-otlp-grpc": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.8.0", "@opentelemetry/exporter-metrics-otlp-http": "0.34.0", "@opentelemetry/otlp-grpc-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-metrics": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/exporter-metrics-otlp-http": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-metrics": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/exporter-metrics-otlp-proto": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/exporter-metrics-otlp-http": "0.34.0", "@opentelemetry/otlp-exporter-base": "0.34.0", "@opentelemetry/otlp-proto-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-metrics": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/exporter-trace-otlp-grpc": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-grpc-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/exporter-trace-otlp-http": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/exporter-trace-otlp-proto": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-exporter-base": "0.34.0", "@opentelemetry/otlp-proto-exporter-base": "0.34.0", "@opentelemetry/otlp-transformer": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/exporter-zipkin": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0", "@opentelemetry/semantic-conventions": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/exporter-zipkin/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation": {"version": "0.40.0", "license": "Apache-2.0", "dependencies": {"@types/shimmer": "^1.0.2", "import-in-the-middle": "1.3.5", "require-in-the-middle": "^7.1.0", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-amqplib": {"version": "0.32.5", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-amqplib/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-amqplib/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-aws-lambda": {"version": "0.35.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/propagator-aws-xray": "^1.2.1", "@opentelemetry/resources": "^1.8.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/aws-lambda": "8.10.81"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-aws-lambda/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-aws-lambda/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-aws-lambda/node_modules/@opentelemetry/resources": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-aws-lambda/node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-aws-sdk": {"version": "0.34.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/propagation-utils": "^0.29.5", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-aws-sdk/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-aws-sdk/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-bunyan": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@types/bunyan": "1.8.7"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-cassandra-driver": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-connect": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/connect": "3.4.35"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-connect/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-connect/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-connect/node_modules/@types/connect": {"version": "3.4.35", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@opentelemetry/instrumentation-connect/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@opentelemetry/instrumentation-dataloader": {"version": "0.4.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-dns": {"version": "0.31.5", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "semver": "^7.3.2"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-dns/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/instrumentation-express": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/express": "4.17.13"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-express/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-express/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-express/node_modules/@types/express": {"version": "4.17.13", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@opentelemetry/instrumentation-fastify": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-fastify/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-fastify/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-fs": {"version": "0.7.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-generic-pool": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/generic-pool": "^3.1.9"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-graphql": {"version": "0.34.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-grpc": {"version": "0.38.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "0.38.0", "@opentelemetry/semantic-conventions": "1.12.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-grpc/node_modules/@opentelemetry/instrumentation": {"version": "0.38.0", "license": "Apache-2.0", "dependencies": {"require-in-the-middle": "^6.0.0", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-grpc/node_modules/@opentelemetry/semantic-conventions": {"version": "1.12.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-grpc/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@opentelemetry/instrumentation-grpc/node_modules/require-in-the-middle": {"version": "6.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@opentelemetry/instrumentation-grpc/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/instrumentation-hapi": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/hapi__hapi": "20.0.9"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-hapi/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-hapi/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-http": {"version": "0.38.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.12.0", "@opentelemetry/instrumentation": "0.38.0", "@opentelemetry/semantic-conventions": "1.12.0", "semver": "^7.3.5"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/core": {"version": "1.12.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.12.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.5.0"}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation": {"version": "0.38.0", "license": "Apache-2.0", "dependencies": {"require-in-the-middle": "^6.0.0", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/semantic-conventions": {"version": "1.12.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/require-in-the-middle": {"version": "6.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@opentelemetry/instrumentation-http/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/instrumentation-ioredis": {"version": "0.34.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/redis-common": "^0.35.1", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/ioredis4": "npm:@types/ioredis@^4.28.10"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-knex": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-koa": {"version": "0.34.6", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/koa": "2.13.6", "@types/koa__router": "8.0.7"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-koa/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-koa/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-koa/node_modules/@types/koa": {"version": "2.13.6", "license": "MIT", "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}}, "node_modules/@opentelemetry/instrumentation-koa/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@opentelemetry/instrumentation-lru-memoizer": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-memcached": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/memcached": "^2.2.6"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-mongodb": {"version": "0.34.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.39.1", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-mongodb/node_modules/@opentelemetry/instrumentation": {"version": "0.39.1", "license": "Apache-2.0", "dependencies": {"require-in-the-middle": "^7.1.0", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-mongodb/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/instrumentation-mongoose": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14.0"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-mongoose/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-mongoose/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-mysql": {"version": "0.33.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/mysql": "2.15.19"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-mysql2": {"version": "0.33.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-nestjs-core": {"version": "0.32.5", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-net": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-pg": {"version": "0.35.3", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/pg": "8.6.1", "@types/pg-pool": "2.0.3"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-pino": {"version": "0.33.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-redis": {"version": "0.34.7", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/redis-common": "^0.35.1", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-redis-4": {"version": "0.34.6", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/redis-common": "^0.35.1", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-restify": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-restify/node_modules/@opentelemetry/core": {"version": "1.30.1", "license": "Apache-2.0", "dependencies": {"@opentelemetry/semantic-conventions": "1.28.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/instrumentation-restify/node_modules/@opentelemetry/core/node_modules/@opentelemetry/semantic-conventions": {"version": "1.28.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/instrumentation-router": {"version": "0.32.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-socket.io": {"version": "0.33.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "engines": {"node": ">=14.0"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-tedious": {"version": "0.5.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/tedious": "^4.0.6"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation-winston": {"version": "0.31.4", "license": "Apache-2.0", "dependencies": {"@opentelemetry/instrumentation": "^0.40.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/instrumentation/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/otlp-exporter-base": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/otlp-grpc-exporter-base": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@grpc/grpc-js": "^1.7.1", "@grpc/proto-loader": "^0.7.3", "@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-exporter-base": "0.34.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/otlp-proto-exporter-base": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/otlp-exporter-base": "0.34.0", "protobufjs": "7.1.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/otlp-transformer": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-metrics": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.4.0"}}, "node_modules/@opentelemetry/propagation-utils": {"version": "0.29.5", "license": "Apache-2.0", "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}}, "node_modules/@opentelemetry/propagator-aws-xray": {"version": "1.26.2", "license": "Apache-2.0", "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}}, "node_modules/@opentelemetry/propagator-b3": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/propagator-jaeger": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/redis-common": {"version": "0.35.1", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/resources": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/semantic-conventions": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/resources/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/sdk-metrics": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/resources": "1.8.0", "lodash.merge": "4.6.2"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.4.0"}}, "node_modules/@opentelemetry/sdk-node": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/exporter-jaeger": "1.8.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.34.0", "@opentelemetry/exporter-trace-otlp-http": "0.34.0", "@opentelemetry/exporter-trace-otlp-proto": "0.34.0", "@opentelemetry/exporter-zipkin": "1.8.0", "@opentelemetry/instrumentation": "0.34.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/sdk-metrics": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0", "@opentelemetry/sdk-trace-node": "1.8.0", "@opentelemetry/semantic-conventions": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.4.0"}}, "node_modules/@opentelemetry/sdk-node/node_modules/@opentelemetry/instrumentation": {"version": "0.34.0", "license": "Apache-2.0", "dependencies": {"require-in-the-middle": "^5.0.3", "semver": "^7.3.2", "shimmer": "^1.2.1"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}}, "node_modules/@opentelemetry/sdk-node/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/sdk-node/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@opentelemetry/sdk-node/node_modules/require-in-the-middle": {"version": "5.2.0", "license": "MIT", "dependencies": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1"}, "engines": {"node": ">=6"}}, "node_modules/@opentelemetry/sdk-node/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/sdk-trace-base": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/core": "1.8.0", "@opentelemetry/resources": "1.8.0", "@opentelemetry/semantic-conventions": "1.8.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/sdk-trace-base/node_modules/@opentelemetry/semantic-conventions": {"version": "1.8.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@opentelemetry/sdk-trace-node": {"version": "1.8.0", "license": "Apache-2.0", "dependencies": {"@opentelemetry/context-async-hooks": "1.8.0", "@opentelemetry/core": "1.8.0", "@opentelemetry/propagator-b3": "1.8.0", "@opentelemetry/propagator-jaeger": "1.8.0", "@opentelemetry/sdk-trace-base": "1.8.0", "semver": "^7.3.5"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.4.0"}}, "node_modules/@opentelemetry/sdk-trace-node/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@opentelemetry/semantic-conventions": {"version": "1.32.0", "license": "Apache-2.0", "engines": {"node": ">=14"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/path": {"version": "1.1.2", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sentry/core": {"version": "5.6.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.6.1", "@sentry/minimal": "5.6.1", "@sentry/types": "5.6.1", "@sentry/utils": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/core/node_modules/@sentry/hub": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.6.1", "@sentry/utils": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/core/node_modules/@sentry/minimal": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.6.1", "@sentry/types": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/core/node_modules/@sentry/types": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=6"}}, "node_modules/@sentry/core/node_modules/@sentry/utils": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/hub": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/minimal": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/types": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/node": {"version": "5.6.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/core": "5.6.2", "@sentry/hub": "5.6.1", "@sentry/types": "5.6.1", "@sentry/utils": "5.6.1", "cookie": "0.3.1", "https-proxy-agent": "2.2.1", "lru_map": "0.3.3", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/node/node_modules/@sentry/hub": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.6.1", "@sentry/utils": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/node/node_modules/@sentry/types": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=6"}}, "node_modules/@sentry/node/node_modules/@sentry/utils": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.6.1", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/node/node_modules/agent-base": {"version": "4.3.0", "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/@sentry/node/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@sentry/node/node_modules/https-proxy-agent": {"version": "2.2.1", "license": "MIT", "dependencies": {"agent-base": "^4.1.0", "debug": "^3.1.0"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/@sentry/tracing": {"version": "5.30.0", "license": "MIT", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/minimal": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sentry/types": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=6"}}, "node_modules/@sentry/utils": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/types": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/@sideway/address": {"version": "4.1.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sqltools/formatter": {"version": "1.2.5", "license": "MIT"}, "node_modules/@tootallnate/once": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@ts-morph/common": {"version": "0.1.1", "license": "MIT", "optional": true, "dependencies": {"@dsherret/to-absolute-glob": "^2.0.2", "fs-extra": "^8.1.0", "glob-parent": "^5.1.0", "globby": "^10.0.1", "is-negated-glob": "^1.0.0", "multimatch": "^4.0.0", "typescript": "~3.7.2"}}, "node_modules/@ts-morph/common/node_modules/globby": {"version": "10.0.2", "license": "MIT", "optional": true, "dependencies": {"@types/glob": "^7.1.1", "array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.0.3", "glob": "^7.1.3", "ignore": "^5.1.1", "merge2": "^1.2.3", "slash": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@ts-morph/common/node_modules/slash": {"version": "3.0.0", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/@ts-morph/common/node_modules/typescript": {"version": "3.7.7", "license": "Apache-2.0", "optional": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/@types/accepts": {"version": "1.3.7", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/aws-lambda": {"version": "8.10.81", "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.5", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bunyan": {"version": "1.8.7", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cache-manager": {"version": "2.10.3", "dev": true, "license": "MIT"}, "node_modules/@types/connect": {"version": "3.4.38", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/content-disposition": {"version": "0.5.8", "license": "MIT"}, "node_modules/@types/cookiejar": {"version": "2.1.5", "dev": true, "license": "MIT"}, "node_modules/@types/cookies": {"version": "0.9.0", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.10", "license": "MIT"}, "node_modules/@types/cron": {"version": "1.7.3", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "moment": ">=2.14.0"}}, "node_modules/@types/dompurify": {"version": "2.4.0", "dev": true, "license": "MIT", "dependencies": {"@types/trusted-types": "*"}}, "node_modules/@types/express": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/@types/express/-/express-5.0.1.tgz", "integrity": "sha512-UZUw8vjpWFXuDnjFTh7/5c2TWDlQqeXHi6hcN7F2XSVT5P+WmUnnbFS3KA6Jnc6IsEqI2qCVu2bK0R0J4A8ZQQ==", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "5.0.6", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/fs-capacitor": {"version": "2.0.0", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/generic-pool": {"version": "3.8.3", "deprecated": "This is a stub types definition. generic-pool provides its own type definitions, so you do not need this installed.", "license": "MIT", "dependencies": {"generic-pool": "*"}}, "node_modules/@types/glob": {"version": "7.2.0", "license": "MIT", "optional": true, "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/graphql": {"version": "14.2.3", "resolved": "https://registry.npmjs.org/@types/graphql/-/graphql-14.2.3.tgz", "integrity": "sha512-UoCovaxbJIxagCvVfalfK7YaNhmxj3BQFRQ2RHQKLiu+9wNXhJnlbspsLHt/YQM99IaLUUFJNzCwzc6W0ypMeQ==", "license": "MIT", "optional": true, "peer": true}, "node_modules/@types/hapi__catbox": {"version": "10.2.6", "license": "MIT"}, "node_modules/@types/hapi__hapi": {"version": "20.0.9", "license": "MIT", "dependencies": {"@hapi/boom": "^9.0.0", "@hapi/iron": "^6.0.0", "@hapi/podium": "^4.1.3", "@types/hapi__catbox": "*", "@types/hapi__mimos": "*", "@types/hapi__shot": "*", "@types/node": "*", "joi": "^17.3.0"}}, "node_modules/@types/hapi__mimos": {"version": "4.1.4", "license": "MIT", "dependencies": {"@types/mime-db": "*"}}, "node_modules/@types/hapi__shot": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/@types/hapi__shot/-/hapi__shot-6.0.0.tgz", "integrity": "sha512-CUxk0AEQRpIT2cx7EYXkqaXm60LP46ZyEhr/7uov7D/ZLysA5DKkfBtPIYnoWvn8qfcFV/7YHLd3U9gThuhb0A==", "deprecated": "This is a stub types definition. @hapi/shot provides its own type definitions, so you do not need this installed.", "license": "MIT", "dependencies": {"@hapi/shot": "*"}}, "node_modules/@types/http-assert": {"version": "1.5.6", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.4", "license": "MIT"}, "node_modules/@types/ioredis4": {"name": "@types/ioredis", "version": "4.28.10", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT", "optional": true}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "optional": true, "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "1.1.2", "license": "MIT", "optional": true, "dependencies": {"@types/istanbul-lib-coverage": "*", "@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "23.3.14", "dev": true, "license": "MIT"}, "node_modules/@types/json2csv": {"version": "4.5.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/json5": {"version": "0.0.29", "license": "MIT"}, "node_modules/@types/jsonwebtoken": {"version": "8.3.7", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/keygrip": {"version": "1.0.6", "license": "MIT"}, "node_modules/@types/koa": {"version": "2.15.0", "license": "MIT", "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}}, "node_modules/@types/koa__router": {"version": "8.0.7", "license": "MIT", "dependencies": {"@types/koa": "*"}}, "node_modules/@types/koa-compose": {"version": "3.2.8", "license": "MIT", "dependencies": {"@types/koa": "*"}}, "node_modules/@types/long": {"version": "4.0.2", "license": "MIT"}, "node_modules/@types/lossless-json": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/memcached": {"version": "2.2.10", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/methods": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "license": "MIT"}, "node_modules/@types/mime-db": {"version": "1.43.5", "license": "MIT"}, "node_modules/@types/minimatch": {"version": "5.1.2", "license": "MIT", "optional": true}, "node_modules/@types/moment": {"version": "2.13.0", "dev": true, "license": "MIT", "dependencies": {"moment": "*"}}, "node_modules/@types/moment-timezone": {"version": "0.5.30", "dev": true, "license": "MIT", "dependencies": {"moment-timezone": "*"}}, "node_modules/@types/multer": {"version": "1.4.12", "resolved": "https://registry.npmjs.org/@types/multer/-/multer-1.4.12.tgz", "integrity": "sha512-pQ2hoqvXiJt2FP9WQVLPRO+AmiIm/ZYkavPlIQnx282u4ZrVdztx0pkh3jjpQt0Kz+YI0YhSG264y08UJKoUQg==", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/mysql": {"version": "2.15.19", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/node-fetch": {"version": "2.5.7", "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "form-data": "^3.0.0"}}, "node_modules/@types/pg": {"version": "8.6.1", "license": "MIT", "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^2.2.0"}}, "node_modules/@types/pg-pool": {"version": "2.0.3", "license": "MIT", "dependencies": {"@types/pg": "*"}}, "node_modules/@types/pg-pool/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/pg-pool/node_modules/@types/pg": {"version": "8.11.14", "license": "MIT", "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^4.0.1"}}, "node_modules/@types/pg-pool/node_modules/pg-types": {"version": "4.0.2", "license": "MIT", "dependencies": {"pg-int8": "1.0.1", "pg-numeric": "1.0.2", "postgres-array": "~3.0.1", "postgres-bytea": "~3.0.0", "postgres-date": "~2.1.0", "postgres-interval": "^3.0.0", "postgres-range": "^1.1.1"}, "engines": {"node": ">=10"}}, "node_modules/@types/pg-pool/node_modules/postgres-array": {"version": "3.0.4", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/@types/pg-pool/node_modules/postgres-bytea": {"version": "3.0.0", "license": "MIT", "dependencies": {"obuf": "~1.1.2"}, "engines": {"node": ">= 6"}}, "node_modules/@types/pg-pool/node_modules/postgres-date": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/@types/pg-pool/node_modules/postgres-interval": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/@types/qs": {"version": "6.9.18", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "license": "MIT"}, "node_modules/@types/semver": {"version": "6.2.7", "license": "MIT", "optional": true}, "node_modules/@types/send": {"version": "0.17.4", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/shimmer": {"version": "1.2.0", "license": "MIT"}, "node_modules/@types/superagent": {"version": "8.1.9", "dev": true, "license": "MIT", "dependencies": {"@types/cookiejar": "^2.1.5", "@types/methods": "^1.1.4", "@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/superagent/node_modules/form-data": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/@types/supertest": {"version": "2.0.16", "dev": true, "license": "MIT", "dependencies": {"@types/superagent": "*"}}, "node_modules/@types/tedious": {"version": "4.0.14", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "dev": true, "license": "MIT"}, "node_modules/@types/validator": {"version": "13.15.0", "license": "MIT", "optional": true}, "node_modules/@types/ws": {"version": "7.4.7", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/yargs": {"version": "13.0.12", "license": "MIT", "optional": true, "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT", "optional": true}, "node_modules/@types/zen-observable": {"version": "0.8.3", "license": "MIT"}, "node_modules/@wry/equality": {"version": "0.1.11", "license": "MIT", "dependencies": {"tslib": "^1.9.3"}}, "node_modules/abab": {"version": "2.0.6", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/abbrev": {"version": "1.1.1", "license": "ISC"}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "6.0.0", "license": "MIT", "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "node_modules/acorn-globals/node_modules/acorn": {"version": "7.4.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agent-base/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/aggregate-error": {"version": "3.0.1", "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-align": {"version": "2.0.0", "license": "ISC", "dependencies": {"string-width": "^2.0.0"}}, "node_modules/ansi-align/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-align/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-align/node_modules/string-width": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/ansi-align/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/ansi-color": {"version": "0.2.1"}, "node_modules/ansi-escapes": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-regex": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/any-base": {"version": "1.1.0", "license": "MIT"}, "node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "node_modules/anymatch": {"version": "2.0.0", "license": "ISC", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/anymatch/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/anymatch/node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch/node_modules/normalize-path": {"version": "2.1.1", "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/apollo-cache-control": {"version": "0.15.0", "license": "MIT", "dependencies": {"apollo-server-env": "^3.2.0", "apollo-server-plugin-base": "^0.14.0"}, "engines": {"node": ">=6.0"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-datasource": {"version": "0.10.0", "license": "MIT", "dependencies": {"apollo-server-caching": "^0.7.0", "apollo-server-env": "^3.2.0"}, "engines": {"node": ">=6"}}, "node_modules/apollo-engine-reporting-protobuf": {"version": "0.4.4", "license": "MIT", "optional": true, "dependencies": {"@apollo/protobufjs": "^1.0.3"}}, "node_modules/apollo-engine-reporting-protobuf/node_modules/@apollo/protobufjs": {"version": "1.2.7", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "long": "^4.0.0"}, "bin": {"apollo-pbjs": "bin/pbjs", "apollo-pbts": "bin/pbts"}}, "node_modules/apollo-engine-reporting-protobuf/node_modules/long": {"version": "4.0.0", "license": "Apache-2.0", "optional": true}, "node_modules/apollo-env": {"version": "0.6.6", "license": "MIT", "optional": true, "dependencies": {"@types/node-fetch": "2.5.7", "core-js": "^3.0.1", "node-fetch": "^2.2.0", "sha.js": "^2.4.11"}, "engines": {"node": ">=8"}}, "node_modules/apollo-graphql": {"version": "0.4.5", "license": "MIT", "optional": true, "dependencies": {"apollo-env": "^0.6.5", "lodash.sortby": "^4.7.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^14.2.1"}}, "node_modules/apollo-link": {"version": "1.2.14", "license": "MIT", "dependencies": {"apollo-utilities": "^1.3.0", "ts-invariant": "^0.4.0", "tslib": "^1.9.3", "zen-observable-ts": "^0.8.21"}, "peerDependencies": {"graphql": "^0.11.3 || ^0.12.3 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-link/node_modules/zen-observable-ts": {"version": "0.8.21", "license": "MIT", "dependencies": {"tslib": "^1.9.3", "zen-observable": "^0.8.0"}}, "node_modules/apollo-reporting-protobuf": {"version": "0.8.0", "license": "MIT", "dependencies": {"@apollo/protobufjs": "1.2.2"}}, "node_modules/apollo-server-caching": {"version": "0.7.0", "license": "MIT", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=6"}}, "node_modules/apollo-server-core": {"version": "2.26.2", "license": "MIT", "dependencies": {"@apollographql/apollo-tools": "^0.5.0", "@apollographql/graphql-playground-html": "1.6.27", "@apollographql/graphql-upload-8-fork": "^8.1.4", "@josephg/resolvable": "^1.0.0", "@types/ws": "^7.0.0", "apollo-cache-control": "^0.15.0", "apollo-datasource": "^0.10.0", "apollo-graphql": "^0.9.0", "apollo-reporting-protobuf": "^0.8.0", "apollo-server-caching": "^0.7.0", "apollo-server-env": "^3.2.0", "apollo-server-errors": "^2.5.0", "apollo-server-plugin-base": "^0.14.0", "apollo-server-types": "^0.10.0", "apollo-tracing": "^0.16.0", "async-retry": "^1.2.1", "fast-json-stable-stringify": "^2.0.0", "graphql-extensions": "^0.16.0", "graphql-tag": "^2.11.0", "graphql-tools": "^4.0.8", "loglevel": "^1.6.7", "lru-cache": "^6.0.0", "sha.js": "^2.4.11", "subscriptions-transport-ws": "^0.9.19", "uuid": "^8.0.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-server-core/node_modules/apollo-graphql": {"version": "0.9.7", "license": "MIT", "dependencies": {"core-js-pure": "^3.10.2", "lodash.sortby": "^4.7.0", "sha.js": "^2.4.11"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^14.2.1 || ^15.0.0"}}, "node_modules/apollo-server-core/node_modules/graphql-extensions": {"version": "0.16.0", "license": "MIT", "dependencies": {"@apollographql/apollo-tools": "^0.5.0", "apollo-server-env": "^3.2.0", "apollo-server-types": "^0.10.0"}, "engines": {"node": ">=6.0"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-server-core/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/apollo-server-env": {"version": "3.2.0", "license": "MIT", "dependencies": {"node-fetch": "^2.6.1", "util.promisify": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/apollo-server-errors": {"version": "2.5.0", "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-server-express": {"version": "2.26.2", "license": "MIT", "dependencies": {"@apollographql/graphql-playground-html": "1.6.27", "@types/accepts": "^1.3.5", "@types/body-parser": "1.19.0", "@types/cors": "2.8.10", "@types/express": "^4.17.12", "@types/express-serve-static-core": "^4.17.21", "accepts": "^1.3.5", "apollo-server-core": "^2.26.2", "apollo-server-types": "^0.10.0", "body-parser": "^1.18.3", "cors": "^2.8.5", "express": "^4.17.1", "graphql-subscriptions": "^1.0.0", "graphql-tools": "^4.0.8", "parseurl": "^1.3.2", "subscriptions-transport-ws": "^0.9.19", "type-is": "^1.6.16"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-server-express/node_modules/@types/body-parser": {"version": "1.19.0", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/apollo-server-express/node_modules/@types/express": {"version": "4.17.21", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/apollo-server-express/node_modules/@types/express/node_modules/@types/body-parser": {"version": "1.19.5", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/apollo-server-express/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/apollo-server-express/node_modules/body-parser": {"version": "1.20.3", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/apollo-server-express/node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/apollo-server-express/node_modules/cookie": {"version": "0.7.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/apollo-server-express/node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/express": {"version": "4.21.2", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/apollo-server-express/node_modules/finalhandler": {"version": "1.3.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/merge-descriptors": {"version": "1.0.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/apollo-server-express/node_modules/path-to-regexp": {"version": "0.1.12", "license": "MIT"}, "node_modules/apollo-server-express/node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/apollo-server-express/node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-express/node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/apollo-server-express/node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/apollo-server-express/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/apollo-server-plugin-base": {"version": "0.14.0", "license": "MIT", "dependencies": {"apollo-server-types": "^0.10.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-server-types": {"version": "0.10.0", "license": "MIT", "dependencies": {"apollo-reporting-protobuf": "^0.8.0", "apollo-server-caching": "^0.7.0", "apollo-server-env": "^3.2.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-tracing": {"version": "0.16.0", "license": "MIT", "dependencies": {"apollo-server-env": "^3.2.0", "apollo-server-plugin-base": "^0.14.0"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/apollo-utilities": {"version": "1.3.4", "license": "MIT", "dependencies": {"@wry/equality": "^0.1.2", "fast-json-stable-stringify": "^2.0.0", "ts-invariant": "^0.4.0", "tslib": "^1.10.0"}, "peerDependencies": {"graphql": "^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/app-root-path": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/append-field": {"version": "1.0.0", "license": "MIT"}, "node_modules/append-transform": {"version": "0.4.0", "license": "MIT", "dependencies": {"default-require-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/aproba": {"version": "2.0.0", "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "2.0.0", "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10"}}, "node_modules/are-we-there-yet/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/are-we-there-yet/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-differ": {"version": "3.0.0", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/array-equal": {"version": "1.0.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array.prototype.reduce": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-array-method-boxes-properly": "^1.0.0", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "is-string": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arrify": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.2.6", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/assign-symbols": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/astral-regex": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/async": {"version": "3.2.3", "license": "MIT"}, "node_modules/async-each": {"version": "1.0.6", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/async-function": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/async-limiter": {"version": "1.0.1", "license": "MIT"}, "node_modules/async-retry": {"version": "1.3.3", "license": "MIT", "dependencies": {"retry": "0.13.1"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-s3": {"version": "2.0.5", "license": "MIT", "dependencies": {"crypto-js": "^3.1.9-1", "short-uuid": "^3.1.0"}}, "node_modules/aws-sdk": {"version": "2.1692.0", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"buffer": "4.9.2", "events": "1.1.1", "ieee754": "1.1.13", "jmespath": "0.16.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "util": "^0.12.4", "uuid": "8.0.0", "xml2js": "0.6.2"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/aws-sdk/node_modules/ieee754": {"version": "1.1.13", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/aws-sdk/node_modules/sax": {"version": "1.2.1", "license": "ISC"}, "node_modules/aws-sdk/node_modules/uuid": {"version": "8.0.0", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/aws-sign2": {"version": "0.7.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.13.2", "license": "MIT"}, "node_modules/axios": {"version": "0.19.2", "license": "MIT", "dependencies": {"follow-redirects": "1.5.10"}}, "node_modules/babel-code-frame": {"version": "6.26.0", "license": "MIT", "dependencies": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}}, "node_modules/babel-code-frame/node_modules/ansi-styles": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/chalk": {"version": "1.1.3", "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/js-tokens": {"version": "3.0.2", "license": "MIT"}, "node_modules/babel-code-frame/node_modules/strip-ansi": {"version": "3.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/supports-color": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/babel-core": {"version": "6.26.3", "license": "MIT", "dependencies": {"babel-code-frame": "^6.26.0", "babel-generator": "^6.26.0", "babel-helpers": "^6.24.1", "babel-messages": "^6.23.0", "babel-register": "^6.26.0", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "convert-source-map": "^1.5.1", "debug": "^2.6.9", "json5": "^0.5.1", "lodash": "^4.17.4", "minimatch": "^3.0.4", "path-is-absolute": "^1.0.1", "private": "^0.1.8", "slash": "^1.0.0", "source-map": "^0.5.7"}}, "node_modules/babel-core/node_modules/json5": {"version": "0.5.1", "license": "MIT", "bin": {"json5": "lib/cli.js"}}, "node_modules/babel-generator": {"version": "6.26.1", "license": "MIT", "dependencies": {"babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.17.4", "source-map": "^0.5.7", "trim-right": "^1.0.1"}}, "node_modules/babel-helpers": {"version": "6.24.1", "license": "MIT", "dependencies": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "node_modules/babel-jest": {"version": "23.6.0", "license": "MIT", "dependencies": {"babel-plugin-istanbul": "^4.1.6", "babel-preset-jest": "^23.2.0"}, "peerDependencies": {"babel-core": "^6.0.0 || ^7.0.0-0"}}, "node_modules/babel-messages": {"version": "6.23.0", "license": "MIT", "dependencies": {"babel-runtime": "^6.22.0"}}, "node_modules/babel-plugin-istanbul": {"version": "4.1.6", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"babel-plugin-syntax-object-rest-spread": "^6.13.0", "find-up": "^2.1.0", "istanbul-lib-instrument": "^1.10.1", "test-exclude": "^4.2.1"}}, "node_modules/babel-plugin-jest-hoist": {"version": "23.2.0", "license": "MIT"}, "node_modules/babel-plugin-syntax-object-rest-spread": {"version": "6.13.0", "license": "MIT"}, "node_modules/babel-preset-jest": {"version": "23.2.0", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^23.2.0", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}}, "node_modules/babel-register": {"version": "6.26.0", "license": "MIT", "dependencies": {"babel-core": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^2.5.0", "home-or-tmp": "^2.0.0", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "source-map-support": "^0.4.15"}}, "node_modules/babel-register/node_modules/core-js": {"version": "2.6.12", "hasInstallScript": true, "license": "MIT"}, "node_modules/babel-register/node_modules/source-map-support": {"version": "0.4.18", "license": "MIT", "dependencies": {"source-map": "^0.5.6"}}, "node_modules/babel-runtime": {"version": "6.26.0", "license": "MIT", "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/babel-runtime/node_modules/core-js": {"version": "2.6.12", "hasInstallScript": true, "license": "MIT"}, "node_modules/babel-template": {"version": "6.26.0", "license": "MIT", "dependencies": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}}, "node_modules/babel-traverse": {"version": "6.26.0", "license": "MIT", "dependencies": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}}, "node_modules/babel-types": {"version": "6.26.0", "license": "MIT", "dependencies": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}}, "node_modules/babylon": {"version": "6.18.0", "license": "MIT", "bin": {"babylon": "bin/babylon.js"}}, "node_modules/backo2": {"version": "1.0.2", "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt": {"version": "5.1.1", "hasInstallScript": true, "license": "MIT", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.11", "node-addon-api": "^5.0.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/binary-extensions": {"version": "1.13.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/bluebird": {"version": "3.7.2", "license": "MIT"}, "node_modules/body-parser": {"version": "1.19.0", "license": "MIT", "dependencies": {"bytes": "3.1.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "on-finished": "~2.3.0", "qs": "6.7.0", "raw-body": "2.4.0", "type-is": "~1.6.17"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/http-errors": {"version": "1.7.2", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/body-parser/node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/body-parser/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/qs": {"version": "6.7.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/bowser": {"version": "2.9.0", "license": "MIT"}, "node_modules/boxen": {"version": "1.3.0", "license": "MIT", "dependencies": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/boxen/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/boxen/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/boxen/node_modules/string-width": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/boxen/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/browser-process-hrtime": {"version": "1.0.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/browser-resolve": {"version": "1.11.3", "license": "MIT", "dependencies": {"resolve": "1.1.7"}}, "node_modules/browser-resolve/node_modules/resolve": {"version": "1.1.7", "license": "MIT"}, "node_modules/bs-logger": {"version": "0.2.6", "dev": true, "license": "MIT", "dependencies": {"fast-json-stable-stringify": "2.x"}, "engines": {"node": ">= 6"}}, "node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "4.9.2", "license": "MIT", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/bufrw": {"version": "1.4.0", "dependencies": {"ansi-color": "^0.2.1", "error": "^7.0.0", "hexer": "^1.5.0", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.10.x"}}, "node_modules/bufrw/node_modules/error": {"version": "7.2.1", "dependencies": {"string-template": "~0.2.1"}}, "node_modules/builtin-modules": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/busboy": {"version": "0.2.14", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/busboy/node_modules/isarray": {"version": "0.0.1", "license": "MIT"}, "node_modules/busboy/node_modules/readable-stream": {"version": "1.1.14", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/busboy/node_modules/string_decoder": {"version": "0.10.31", "license": "MIT"}, "node_modules/bytes": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cache-base": {"version": "1.0.1", "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cache-manager": {"version": "3.6.3", "license": "MIT", "dependencies": {"async": "3.2.3", "lodash.clonedeep": "^4.5.0", "lru-cache": "6.0.0"}}, "node_modules/call-bind": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/camelcase": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/camelize": {"version": "1.0.0", "license": "MIT"}, "node_modules/capture-exit": {"version": "1.2.0", "license": "ISC", "dependencies": {"rsvp": "^3.3.3"}}, "node_modules/capture-stack-trace": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/caseless": {"version": "0.12.0", "license": "Apache-2.0"}, "node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chokidar": {"version": "2.1.8", "license": "MIT", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "3.1.0", "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/chokidar/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chokidar/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/chokidar/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chownr": {"version": "1.1.4", "license": "ISC"}, "node_modules/ci-info": {"version": "1.6.0", "license": "MIT"}, "node_modules/class-transformer": {"version": "0.2.3", "license": "MIT"}, "node_modules/class-utils": {"version": "0.3.6", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-validator": {"version": "0.9.1", "license": "MIT", "dependencies": {"google-libphonenumber": "^3.1.6", "validator": "10.4.0"}}, "node_modules/clean-stack": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-boxes": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/cli-color": {"version": "2.0.0", "license": "ISC", "dependencies": {"ansi-regex": "^2.1.1", "d": "^1.0.1", "es5-ext": "^0.10.51", "es6-iterator": "^2.0.3", "memoizee": "^0.4.14", "timers-ext": "^0.1.7"}}, "node_modules/cli-highlight": {"version": "2.1.11", "license": "ISC", "dependencies": {"chalk": "^4.0.0", "highlight.js": "^10.7.1", "mz": "^2.4.0", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^6.0.0", "yargs": "^16.0.0"}, "bin": {"highlight": "bin/highlight"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}}, "node_modules/cli-highlight/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/cli-highlight/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/cli-highlight/node_modules/cliui": {"version": "7.0.4", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/cli-highlight/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/cli-highlight/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/cli-highlight/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cli-highlight/node_modules/parse5": {"version": "5.1.1", "license": "MIT"}, "node_modules/cli-highlight/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-highlight/node_modules/yargs": {"version": "16.2.0", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/cli-highlight/node_modules/yargs-parser": {"version": "20.2.9", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/cliui": {"version": "4.1.0", "license": "ISC", "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/string-width": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "2.1.0", "license": "MIT", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cliui/node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/cliui/node_modules/wrap-ansi/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cliui/node_modules/wrap-ansi/node_modules/string-width": {"version": "1.0.2", "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cliui/node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "3.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/co": {"version": "4.6.0", "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/code-block-writer": {"version": "10.1.1", "license": "MIT", "optional": true}, "node_modules/code-point-at": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/collection-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/component-emitter": {"version": "1.3.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/configstore": {"version": "3.1.5", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^4.2.1", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/configstore/node_modules/make-dir": {"version": "1.3.0", "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/configstore/node_modules/pify": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/consola": {"version": "2.15.3", "license": "MIT"}, "node_modules/console-control-strings": {"version": "1.1.0", "license": "ISC"}, "node_modules/content-disposition": {"version": "0.5.3", "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-disposition/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/content-security-policy-builder": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/content-type": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/cookie": {"version": "0.3.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/cookiejar": {"version": "2.1.4", "dev": true, "license": "MIT"}, "node_modules/copy-descriptor": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/core-js": {"version": "3.41.0", "hasInstallScript": true, "license": "MIT", "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-pure": {"version": "3.41.0", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/create-error-class": {"version": "3.0.2", "license": "MIT", "dependencies": {"capture-stack-trace": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cron": {"version": "1.8.2", "license": "MIT", "dependencies": {"moment-timezone": "^0.5.x"}}, "node_modules/cross-spawn": {"version": "6.0.6", "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/crypto-js": {"version": "3.3.0", "license": "MIT"}, "node_modules/crypto-random-string": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cssfilter": {"version": "0.0.10", "license": "MIT"}, "node_modules/cssom": {"version": "0.3.8", "license": "MIT"}, "node_modules/cssstyle": {"version": "2.3.0", "license": "MIT", "dependencies": {"cssom": "~0.3.6"}, "engines": {"node": ">=8"}}, "node_modules/d": {"version": "1.0.2", "license": "ISC", "dependencies": {"es5-ext": "^0.10.64", "type": "^2.7.2"}, "engines": {"node": ">=0.12"}}, "node_modules/dashdash": {"version": "1.14.1", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/dasherize": {"version": "2.0.0", "license": "MIT"}, "node_modules/data-urls": {"version": "2.0.0", "license": "MIT", "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/data-view-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decimal.js": {"version": "10.5.0", "license": "MIT"}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/deep-is": {"version": "0.1.4", "license": "MIT"}, "node_modules/deepmerge": {"version": "4.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-require-extensions": {"version": "1.0.0", "license": "MIT", "dependencies": {"strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "license": "MIT"}, "node_modules/depd": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/deprecated-decorator": {"version": "0.1.6", "license": "MIT"}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-indent": {"version": "4.0.0", "license": "MIT", "dependencies": {"repeating": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/detect-libc": {"version": "2.0.4", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/detect-newline": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/dicer": {"version": "0.2.5", "dependencies": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "engines": {"node": ">=0.8.0"}}, "node_modules/dicer/node_modules/isarray": {"version": "0.0.1", "license": "MIT"}, "node_modules/dicer/node_modules/readable-stream": {"version": "1.1.14", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/dicer/node_modules/string_decoder": {"version": "0.10.31", "license": "MIT"}, "node_modules/diff": {"version": "3.5.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/domexception": {"version": "2.0.1", "license": "MIT", "dependencies": {"webidl-conversions": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "5.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/dompurify": {"version": "2.5.8", "license": "(MPL-2.0 OR Apache-2.0)"}, "node_modules/dont-sniff-mimetype": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/dot-prop": {"version": "4.2.1", "license": "MIT", "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/dotenv": {"version": "7.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=6"}}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexer3": {"version": "0.1.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/error": {"version": "7.0.2", "dependencies": {"string-template": "~0.2.1", "xtend": "~4.0.0"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-abstract": {"version": "1.23.9", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.3", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.0", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-regex": "^1.2.1", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.0", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.3", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.3", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.18"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-array-method-boxes-properly": {"version": "1.0.0", "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es5-ext": {"version": "0.10.64", "hasInstallScript": true, "license": "ISC", "dependencies": {"es6-iterator": "^2.0.3", "es6-symbol": "^3.1.3", "esniff": "^2.0.1", "next-tick": "^1.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/es6-iterator": {"version": "2.0.3", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-promise": {"version": "4.2.8", "license": "MIT"}, "node_modules/es6-promisify": {"version": "5.0.0", "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/es6-symbol": {"version": "3.1.4", "license": "ISC", "dependencies": {"d": "^1.0.2", "ext": "^1.7.0"}, "engines": {"node": ">=0.12"}}, "node_modules/es6-weak-map": {"version": "2.0.3", "license": "ISC", "dependencies": {"d": "1", "es5-ext": "^0.10.46", "es6-iterator": "^2.0.3", "es6-symbol": "^3.1.1"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/esniff": {"version": "2.0.1", "license": "ISC", "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}, "engines": {"node": ">=0.10"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-emitter": {"version": "0.3.5", "license": "MIT", "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/eventemitter3": {"version": "3.1.2", "license": "MIT"}, "node_modules/events": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">=0.4.x"}}, "node_modules/exec-sh": {"version": "0.2.2", "license": "MIT", "dependencies": {"merge": "^1.2.0"}}, "node_modules/execa": {"version": "1.0.0", "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/execa/node_modules/get-stream": {"version": "4.1.0", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/exit": {"version": "0.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/expand-brackets": {"version": "0.1.5", "license": "MIT", "dependencies": {"is-posix-bracket": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-range": {"version": "1.8.2", "license": "MIT", "dependencies": {"fill-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-range/node_modules/fill-range": {"version": "2.2.4", "license": "MIT", "dependencies": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^3.0.0", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-range/node_modules/is-number": {"version": "2.1.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-range/node_modules/isobject": {"version": "2.1.0", "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expect": {"version": "23.6.0", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "jest-diff": "^23.6.0", "jest-get-type": "^22.1.0", "jest-matcher-utils": "^23.6.0", "jest-message-util": "^23.4.0", "jest-regex-util": "^23.3.0"}}, "node_modules/express": {"version": "4.17.1", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "array-flatten": "1.1.1", "body-parser": "1.19.0", "content-disposition": "0.5.3", "content-type": "~1.0.4", "cookie": "0.4.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "~1.1.2", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.5", "qs": "6.7.0", "range-parser": "~1.2.1", "safe-buffer": "5.1.2", "send": "0.17.1", "serve-static": "1.14.1", "setprototypeof": "1.1.1", "statuses": "~1.5.0", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/cookie": {"version": "0.4.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/path-to-regexp": {"version": "0.1.7", "license": "MIT"}, "node_modules/express/node_modules/qs": {"version": "6.7.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/express/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/ext": {"version": "1.7.0", "license": "ISC", "dependencies": {"type": "^2.7.2"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "0.3.2", "license": "MIT", "dependencies": {"is-extglob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extsprintf": {"version": "1.3.0", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/fast-glob/node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/fast-glob/node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/fast-glob/node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/fast-glob/node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "license": "MIT"}, "node_modules/fast-safe-stringify": {"version": "2.0.7", "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fb-watchman": {"version": "2.0.2", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/feature-policy": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/filename-regex": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/fileset": {"version": "2.0.3", "license": "MIT", "dependencies": {"glob": "^7.0.3", "minimatch": "^3.0.3"}}, "node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "1.1.2", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "2.1.0", "license": "MIT", "dependencies": {"locate-path": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/follow-redirects": {"version": "1.5.10", "license": "MIT", "dependencies": {"debug": "=3.1.0"}, "engines": {"node": ">=4.0"}}, "node_modules/follow-redirects/node_modules/debug": {"version": "3.1.0", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/follow-redirects/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/for-each": {"version": "0.3.5", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/for-in": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/for-own": {"version": "0.1.5", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/forever-agent": {"version": "0.6.1", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "3.0.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35"}, "engines": {"node": ">= 6"}}, "node_modules/formidable": {"version": "1.2.6", "dev": true, "license": "MIT", "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-capacitor": {"version": "2.0.4", "license": "MIT", "engines": {"node": ">=8.5"}}, "node_modules/fs-extra": {"version": "8.1.0", "license": "MIT", "optional": true, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-extra/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">= 4.0.0"}}, "node_modules/fs-minipass": {"version": "1.2.7", "license": "ISC", "dependencies": {"minipass": "^2.6.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gauge": {"version": "3.0.2", "license": "ISC", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2"}, "engines": {"node": ">=10"}}, "node_modules/generic-pool": {"version": "3.9.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-value": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/getpass": {"version": "0.1.7", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-base": {"version": "0.3.0", "license": "MIT", "dependencies": {"glob-parent": "^2.0.0", "is-glob": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-base/node_modules/glob-parent": {"version": "2.0.0", "license": "ISC", "dependencies": {"is-glob": "^2.0.0"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-parent/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob-parent/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-dirs": {"version": "0.1.1", "license": "MIT", "dependencies": {"ini": "^1.3.4"}, "engines": {"node": ">=4"}}, "node_modules/globals": {"version": "9.18.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/globalthis": {"version": "1.0.4", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/globby": {"version": "11.0.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.1.1", "ignore": "^5.1.4", "merge2": "^1.3.0", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby/node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/google-libphonenumber": {"version": "3.2.40", "license": "(MIT AND Apache-2.0)", "engines": {"node": ">=0.10"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "6.7.1", "license": "MIT", "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graphql": {"version": "14.7.0", "license": "MIT", "dependencies": {"iterall": "^1.2.2"}, "engines": {"node": ">= 6.x"}}, "node_modules/graphql-extensions": {"version": "0.10.10", "license": "MIT", "optional": true, "dependencies": {"@apollographql/apollo-tools": "^0.4.3", "apollo-server-env": "^2.4.3", "apollo-server-types": "^0.2.10"}, "engines": {"node": ">=6.0"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0"}}, "node_modules/graphql-extensions/node_modules/@apollographql/apollo-tools": {"version": "0.4.14", "license": "MIT", "optional": true, "dependencies": {"apollo-env": "^0.9.2"}, "engines": {"node": ">=8", "npm": ">=6"}}, "node_modules/graphql-extensions/node_modules/@types/node": {"version": "22.14.1", "license": "MIT", "optional": true, "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/graphql-extensions/node_modules/@types/node-fetch": {"version": "2.5.10", "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "form-data": "^3.0.0"}}, "node_modules/graphql-extensions/node_modules/apollo-env": {"version": "0.9.2", "license": "MIT", "optional": true, "dependencies": {"@types/node-fetch": "2.5.10", "core-js": "^3.0.1", "node-fetch": "^2.2.0", "sha.js": "^2.4.11"}, "engines": {"node": ">=8"}}, "node_modules/graphql-extensions/node_modules/apollo-server-caching": {"version": "0.5.3", "license": "MIT", "optional": true, "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=6"}}, "node_modules/graphql-extensions/node_modules/apollo-server-env": {"version": "2.4.5", "license": "MIT", "optional": true, "dependencies": {"node-fetch": "^2.1.2", "util.promisify": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/graphql-extensions/node_modules/apollo-server-types": {"version": "0.2.10", "license": "MIT", "optional": true, "dependencies": {"apollo-engine-reporting-protobuf": "^0.4.4", "apollo-server-caching": "^0.5.1", "apollo-server-env": "^2.4.3"}, "engines": {"node": ">=6"}, "peerDependencies": {"graphql": "^0.12.0 || ^0.13.0 || ^14.0.0"}}, "node_modules/graphql-query-complexity": {"version": "0.3.0", "license": "MIT", "optional": true, "dependencies": {"lodash.get": "^4.4.2"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0"}}, "node_modules/graphql-subscriptions": {"version": "1.2.1", "license": "MIT", "dependencies": {"iterall": "^1.3.0"}, "peerDependencies": {"graphql": "^0.10.5 || ^0.11.3 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/graphql-tag": {"version": "2.12.6", "license": "MIT", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/graphql-tag/node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/graphql-tools": {"version": "4.0.8", "license": "MIT", "dependencies": {"apollo-link": "^1.2.14", "apollo-utilities": "^1.0.1", "deprecated-decorator": "^0.1.6", "iterall": "^1.1.3", "uuid": "^3.1.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0"}}, "node_modules/growly": {"version": "1.3.0", "license": "MIT"}, "node_modules/handlebars": {"version": "4.7.8", "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/handlebars/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/har-schema": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has-ansi": {"version": "2.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-bigints": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "license": "ISC"}, "node_modules/has-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/helmet": {"version": "3.23.3", "license": "MIT", "dependencies": {"depd": "2.0.0", "dont-sniff-mimetype": "1.1.0", "feature-policy": "0.3.0", "helmet-crossdomain": "0.4.0", "helmet-csp": "2.10.0", "hide-powered-by": "1.1.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/helmet-crossdomain": {"version": "0.4.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/helmet-csp": {"version": "2.10.0", "license": "MIT", "dependencies": {"bowser": "2.9.0", "camelize": "1.0.0", "content-security-policy-builder": "2.1.0", "dasherize": "2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/helmet/node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/hexer": {"version": "1.5.0", "dependencies": {"ansi-color": "^0.2.1", "minimist": "^1.1.0", "process": "^0.10.0", "xtend": "^4.0.0"}, "bin": {"hexer": "cli.js"}, "engines": {"node": ">= 0.10.x"}}, "node_modules/hide-powered-by": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/highlight.js": {"version": "10.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/home-or-tmp": {"version": "2.0.0", "license": "MIT", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hosted-git-info": {"version": "2.8.9", "license": "ISC"}, "node_modules/hpkp": {"version": "2.0.0", "license": "MIT"}, "node_modules/hsts": {"version": "2.2.0", "license": "MIT", "dependencies": {"depd": "2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/hsts/node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/html-encoding-sniffer": {"version": "2.0.1", "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.5"}, "engines": {"node": ">=10"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-proxy-agent/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/http-signature": {"version": "1.2.0", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/ignore-by-default": {"version": "1.0.1", "license": "ISC"}, "node_modules/import-in-the-middle": {"version": "1.3.5", "license": "Apache-2.0", "dependencies": {"module-details-from-path": "^1.0.3"}}, "node_modules/import-lazy": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/import-local": {"version": "1.0.0", "license": "MIT", "dependencies": {"pkg-dir": "^2.0.0", "resolve-cwd": "^2.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/internal-slot": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/invariant": {"version": "2.2.4", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/invert-kv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is_js": {"version": "0.9.0", "license": "MIT"}, "node_modules/is-absolute": {"version": "1.0.0", "license": "MIT", "optional": true, "dependencies": {"is-relative": "^1.0.0", "is-windows": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-accessor-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/is-arguments": {"version": "1.2.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-async-function": {"version": "2.1.1", "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "1.0.1", "license": "MIT", "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-buffer": {"version": "1.1.6", "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "1.2.1", "license": "MIT", "dependencies": {"ci-info": "^1.5.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-data-view": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-descriptor": {"version": "1.0.3", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-dotfile": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-equal-shallow": {"version": "0.1.3", "license": "MIT", "dependencies": {"is-primitive": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-finite": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-generator-function": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extglob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-installed-globally": {"version": "0.1.0", "license": "MIT", "dependencies": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negated-glob": {"version": "1.0.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-npm": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-obj": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-inside": {"version": "1.0.1", "license": "MIT", "dependencies": {"path-is-inside": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-posix-bracket": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "license": "MIT"}, "node_modules/is-primitive": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-promise": {"version": "2.2.2", "license": "MIT"}, "node_modules/is-redirect": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-relative": {"version": "1.0.0", "license": "MIT", "optional": true, "dependencies": {"is-unc-path": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-retry-allowed": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-string": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-unc-path": {"version": "1.0.0", "license": "MIT", "optional": true, "dependencies": {"unc-path-regex": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-utf8": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-windows": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isstream": {"version": "0.1.2", "license": "MIT"}, "node_modules/istanbul-api": {"version": "1.3.7", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"async": "^2.1.4", "fileset": "^2.0.2", "istanbul-lib-coverage": "^1.2.1", "istanbul-lib-hook": "^1.2.2", "istanbul-lib-instrument": "^1.10.2", "istanbul-lib-report": "^1.1.5", "istanbul-lib-source-maps": "^1.2.6", "istanbul-reports": "^1.5.1", "js-yaml": "^3.7.0", "mkdirp": "^0.5.1", "once": "^1.4.0"}}, "node_modules/istanbul-api/node_modules/async": {"version": "2.6.4", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/istanbul-lib-coverage": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/istanbul-lib-hook": {"version": "1.2.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"append-transform": "^0.4.0"}}, "node_modules/istanbul-lib-instrument": {"version": "1.10.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"babel-generator": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-types": "^6.18.0", "babylon": "^6.18.0", "istanbul-lib-coverage": "^1.2.1", "semver": "^5.3.0"}}, "node_modules/istanbul-lib-report": {"version": "1.1.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^1.2.1", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}}, "node_modules/istanbul-lib-report/node_modules/has-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul-lib-report/node_modules/supports-color": {"version": "3.2.3", "license": "MIT", "dependencies": {"has-flag": "^1.0.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/istanbul-lib-source-maps": {"version": "1.2.6", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^3.1.0", "istanbul-lib-coverage": "^1.2.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3"}}, "node_modules/istanbul-lib-source-maps/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/istanbul-reports": {"version": "1.5.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"handlebars": "^4.0.3"}}, "node_modules/iterall": {"version": "1.3.0", "license": "MIT"}, "node_modules/iterare": {"version": "1.2.0", "license": "ISC", "engines": {"node": ">=6"}}, "node_modules/jaeger-client": {"version": "3.19.0", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0", "opentracing": "^0.14.4", "thriftrw": "^3.5.0", "uuid": "^8.3.2", "xorshift": "^1.1.1"}, "engines": {"node": ">=10"}}, "node_modules/jaeger-client/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/jest": {"version": "23.6.0", "license": "MIT", "dependencies": {"import-local": "^1.0.0", "jest-cli": "^23.6.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": ">= 6"}}, "node_modules/jest-changed-files": {"version": "23.4.2", "license": "MIT", "dependencies": {"throat": "^4.0.0"}}, "node_modules/jest-cli": {"version": "23.6.0", "license": "MIT", "dependencies": {"ansi-escapes": "^3.0.0", "chalk": "^2.0.1", "exit": "^0.1.2", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "import-local": "^1.0.0", "is-ci": "^1.0.10", "istanbul-api": "^1.3.1", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-instrument": "^1.10.1", "istanbul-lib-source-maps": "^1.2.4", "jest-changed-files": "^23.4.2", "jest-config": "^23.6.0", "jest-environment-jsdom": "^23.4.0", "jest-get-type": "^22.1.0", "jest-haste-map": "^23.6.0", "jest-message-util": "^23.4.0", "jest-regex-util": "^23.3.0", "jest-resolve-dependencies": "^23.6.0", "jest-runner": "^23.6.0", "jest-runtime": "^23.6.0", "jest-snapshot": "^23.6.0", "jest-util": "^23.4.0", "jest-validate": "^23.6.0", "jest-watcher": "^23.4.0", "jest-worker": "^23.2.0", "micromatch": "^2.3.11", "node-notifier": "^5.2.1", "prompts": "^0.1.9", "realpath-native": "^1.0.0", "rimraf": "^2.5.4", "slash": "^1.0.0", "string-length": "^2.0.0", "strip-ansi": "^4.0.0", "which": "^1.2.12", "yargs": "^11.0.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": ">= 6"}}, "node_modules/jest-cli/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/jest-cli/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/jest-config": {"version": "23.6.0", "license": "MIT", "dependencies": {"babel-core": "^6.0.0", "babel-jest": "^23.6.0", "chalk": "^2.0.1", "glob": "^7.1.1", "jest-environment-jsdom": "^23.4.0", "jest-environment-node": "^23.4.0", "jest-get-type": "^22.1.0", "jest-jasmine2": "^23.6.0", "jest-regex-util": "^23.3.0", "jest-resolve": "^23.6.0", "jest-util": "^23.4.0", "jest-validate": "^23.6.0", "micromatch": "^2.3.11", "pretty-format": "^23.6.0"}}, "node_modules/jest-diff": {"version": "23.6.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1", "diff": "^3.2.0", "jest-get-type": "^22.1.0", "pretty-format": "^23.6.0"}}, "node_modules/jest-docblock": {"version": "23.2.0", "license": "MIT", "dependencies": {"detect-newline": "^2.1.0"}}, "node_modules/jest-each": {"version": "23.6.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1", "pretty-format": "^23.6.0"}}, "node_modules/jest-environment-jsdom": {"version": "23.4.0", "license": "MIT", "dependencies": {"jest-mock": "^23.2.0", "jest-util": "^23.4.0", "jsdom": "^11.5.1"}}, "node_modules/jest-environment-jsdom/node_modules/acorn": {"version": "5.7.4", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/jest-environment-jsdom/node_modules/acorn-globals": {"version": "4.3.4", "license": "MIT", "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}}, "node_modules/jest-environment-jsdom/node_modules/acorn-globals/node_modules/acorn": {"version": "6.4.2", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/jest-environment-jsdom/node_modules/acorn-walk": {"version": "6.2.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/jest-environment-jsdom/node_modules/cssstyle": {"version": "1.4.0", "license": "MIT", "dependencies": {"cssom": "0.3.x"}}, "node_modules/jest-environment-jsdom/node_modules/data-urls": {"version": "1.1.0", "license": "MIT", "dependencies": {"abab": "^2.0.0", "whatwg-mimetype": "^2.2.0", "whatwg-url": "^7.0.0"}}, "node_modules/jest-environment-jsdom/node_modules/data-urls/node_modules/whatwg-url": {"version": "7.1.0", "license": "MIT", "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "node_modules/jest-environment-jsdom/node_modules/domexception": {"version": "1.0.1", "license": "MIT", "dependencies": {"webidl-conversions": "^4.0.2"}}, "node_modules/jest-environment-jsdom/node_modules/escodegen": {"version": "1.14.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=4.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/jest-environment-jsdom/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/jest-environment-jsdom/node_modules/html-encoding-sniffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.1"}}, "node_modules/jest-environment-jsdom/node_modules/jsdom": {"version": "11.12.0", "license": "MIT", "dependencies": {"abab": "^2.0.0", "acorn": "^5.5.3", "acorn-globals": "^4.1.0", "array-equal": "^1.0.0", "cssom": ">= 0.3.2 < 0.4.0", "cssstyle": "^1.0.0", "data-urls": "^1.0.0", "domexception": "^1.0.1", "escodegen": "^1.9.1", "html-encoding-sniffer": "^1.0.2", "left-pad": "^1.3.0", "nwsapi": "^2.0.7", "parse5": "4.0.0", "pn": "^1.1.0", "request": "^2.87.0", "request-promise-native": "^1.0.5", "sax": "^1.2.4", "symbol-tree": "^3.2.2", "tough-cookie": "^2.3.4", "w3c-hr-time": "^1.0.1", "webidl-conversions": "^4.0.2", "whatwg-encoding": "^1.0.3", "whatwg-mimetype": "^2.1.0", "whatwg-url": "^6.4.1", "ws": "^5.2.0", "xml-name-validator": "^3.0.0"}}, "node_modules/jest-environment-jsdom/node_modules/parse5": {"version": "4.0.0", "license": "MIT"}, "node_modules/jest-environment-jsdom/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/jest-environment-jsdom/node_modules/whatwg-url": {"version": "6.5.0", "license": "MIT", "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "node_modules/jest-environment-jsdom/node_modules/ws": {"version": "5.2.4", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/jest-environment-node": {"version": "23.4.0", "license": "MIT", "dependencies": {"jest-mock": "^23.2.0", "jest-util": "^23.4.0"}}, "node_modules/jest-get-type": {"version": "22.4.3", "license": "MIT"}, "node_modules/jest-haste-map": {"version": "23.6.0", "license": "MIT", "dependencies": {"fb-watchman": "^2.0.0", "graceful-fs": "^4.1.11", "invariant": "^2.2.4", "jest-docblock": "^23.2.0", "jest-serializer": "^23.0.1", "jest-worker": "^23.2.0", "micromatch": "^2.3.11", "sane": "^2.0.0"}}, "node_modules/jest-jasmine2": {"version": "23.6.0", "license": "MIT", "dependencies": {"babel-traverse": "^6.0.0", "chalk": "^2.0.1", "co": "^4.6.0", "expect": "^23.6.0", "is-generator-fn": "^1.0.0", "jest-diff": "^23.6.0", "jest-each": "^23.6.0", "jest-matcher-utils": "^23.6.0", "jest-message-util": "^23.4.0", "jest-snapshot": "^23.6.0", "jest-util": "^23.4.0", "pretty-format": "^23.6.0"}}, "node_modules/jest-leak-detector": {"version": "23.6.0", "license": "MIT", "dependencies": {"pretty-format": "^23.6.0"}}, "node_modules/jest-matcher-utils": {"version": "23.6.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1", "jest-get-type": "^22.1.0", "pretty-format": "^23.6.0"}}, "node_modules/jest-message-util": {"version": "23.4.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0-beta.35", "chalk": "^2.0.1", "micromatch": "^2.3.11", "slash": "^1.0.0", "stack-utils": "^1.0.1"}}, "node_modules/jest-mock": {"version": "23.2.0", "license": "MIT"}, "node_modules/jest-regex-util": {"version": "23.3.0", "license": "MIT"}, "node_modules/jest-resolve": {"version": "23.6.0", "license": "MIT", "dependencies": {"browser-resolve": "^1.11.3", "chalk": "^2.0.1", "realpath-native": "^1.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "23.6.0", "license": "MIT", "dependencies": {"jest-regex-util": "^23.3.0", "jest-snapshot": "^23.6.0"}}, "node_modules/jest-runner": {"version": "23.6.0", "license": "MIT", "dependencies": {"exit": "^0.1.2", "graceful-fs": "^4.1.11", "jest-config": "^23.6.0", "jest-docblock": "^23.2.0", "jest-haste-map": "^23.6.0", "jest-jasmine2": "^23.6.0", "jest-leak-detector": "^23.6.0", "jest-message-util": "^23.4.0", "jest-runtime": "^23.6.0", "jest-util": "^23.4.0", "jest-worker": "^23.2.0", "source-map-support": "^0.5.6", "throat": "^4.0.0"}}, "node_modules/jest-runtime": {"version": "23.6.0", "license": "MIT", "dependencies": {"babel-core": "^6.0.0", "babel-plugin-istanbul": "^4.1.6", "chalk": "^2.0.1", "convert-source-map": "^1.4.0", "exit": "^0.1.2", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.1.11", "jest-config": "^23.6.0", "jest-haste-map": "^23.6.0", "jest-message-util": "^23.4.0", "jest-regex-util": "^23.3.0", "jest-resolve": "^23.6.0", "jest-snapshot": "^23.6.0", "jest-util": "^23.4.0", "jest-validate": "^23.6.0", "micromatch": "^2.3.11", "realpath-native": "^1.0.0", "slash": "^1.0.0", "strip-bom": "3.0.0", "write-file-atomic": "^2.1.0", "yargs": "^11.0.0"}, "bin": {"jest-runtime": "bin/jest-runtime.js"}}, "node_modules/jest-runtime/node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/jest-serializer": {"version": "23.0.1", "license": "MIT"}, "node_modules/jest-snapshot": {"version": "23.6.0", "license": "MIT", "dependencies": {"babel-types": "^6.0.0", "chalk": "^2.0.1", "jest-diff": "^23.6.0", "jest-matcher-utils": "^23.6.0", "jest-message-util": "^23.4.0", "jest-resolve": "^23.6.0", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "pretty-format": "^23.6.0", "semver": "^5.5.0"}}, "node_modules/jest-util": {"version": "23.4.0", "license": "MIT", "dependencies": {"callsites": "^2.0.0", "chalk": "^2.0.1", "graceful-fs": "^4.1.11", "is-ci": "^1.0.10", "jest-message-util": "^23.4.0", "mkdirp": "^0.5.1", "slash": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/jest-util/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/jest-validate": {"version": "23.6.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1", "jest-get-type": "^22.1.0", "leven": "^2.1.0", "pretty-format": "^23.6.0"}}, "node_modules/jest-watcher": {"version": "23.4.0", "license": "MIT", "dependencies": {"ansi-escapes": "^3.0.0", "chalk": "^2.0.1", "string-length": "^2.0.0"}}, "node_modules/jest-worker": {"version": "23.2.0", "license": "MIT", "dependencies": {"merge-stream": "^1.0.1"}}, "node_modules/jmespath": {"version": "0.16.0", "license": "Apache-2.0", "engines": {"node": ">= 0.6.0"}}, "node_modules/joi": {"version": "17.13.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "license": "MIT"}, "node_modules/jsdom": {"version": "16.7.0", "license": "MIT", "dependencies": {"abab": "^2.0.5", "acorn": "^8.2.4", "acorn-globals": "^6.0.0", "cssom": "^0.4.4", "cssstyle": "^2.3.0", "data-urls": "^2.0.0", "decimal.js": "^10.2.1", "domexception": "^2.0.1", "escodegen": "^2.0.0", "form-data": "^3.0.0", "html-encoding-sniffer": "^2.0.1", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.0", "parse5": "6.0.1", "saxes": "^5.0.1", "symbol-tree": "^3.2.4", "tough-cookie": "^4.0.0", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.5.0", "ws": "^7.4.6", "xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsdom/node_modules/cssom": {"version": "0.4.4", "license": "MIT"}, "node_modules/jsdom/node_modules/tough-cookie": {"version": "4.1.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/jsdom/node_modules/webidl-conversions": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/jsesc": {"version": "1.3.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}}, "node_modules/json-schema": {"version": "0.4.0", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "license": "ISC"}, "node_modules/json2csv": {"version": "4.5.4", "license": "MIT", "dependencies": {"commander": "^2.15.1", "jsonparse": "^1.3.1", "lodash.get": "^4.4.2"}, "bin": {"json2csv": "bin/json2csv.js"}}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optional": true, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonparse": {"version": "1.3.1", "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/jsonwebtoken": {"version": "8.5.1", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^5.6.0"}, "engines": {"node": ">=4", "npm": ">=1.4.28"}}, "node_modules/jsprim": {"version": "1.4.2", "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/jwa": {"version": "1.4.1", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/kleur": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/latest-version": {"version": "3.1.0", "license": "MIT", "dependencies": {"package-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/lcid": {"version": "2.0.0", "license": "MIT", "dependencies": {"invert-kv": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/left-pad": {"version": "1.3.0", "license": "WTFPL"}, "node_modules/leven": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/levn": {"version": "0.3.0", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/libphonenumber-js": {"version": "1.12.7", "license": "MIT", "optional": true}, "node_modules/load-json-file": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/locate-path": {"version": "2.0.0", "license": "MIT", "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.camelcase": {"version": "4.3.0", "license": "MIT"}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.get": {"version": "4.4.2", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "license": "MIT"}, "node_modules/lodash.sortby": {"version": "4.7.0", "license": "MIT"}, "node_modules/lodash.xorby": {"version": "4.7.0", "license": "MIT", "optional": true}, "node_modules/loglevel": {"version": "1.9.2", "license": "MIT", "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "node_modules/loglevel-debug": {"version": "0.0.1", "license": "MIT", "optional": true, "dependencies": {"loglevel": "^1.4.0"}}, "node_modules/long": {"version": "5.3.2", "license": "Apache-2.0"}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lossless-json": {"version": "1.0.5", "license": "MIT"}, "node_modules/lowercase-keys": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/lru_map": {"version": "0.3.3", "license": "MIT"}, "node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lru-queue": {"version": "0.1.0", "license": "MIT", "dependencies": {"es5-ext": "~0.10.2"}}, "node_modules/mailchimp-api-v3": {"version": "1.15.0", "license": "MIT", "dependencies": {"bluebird": "^3.4.0", "lodash": "^4.17.14", "request": "^2.88.0", "tar": "^4.0.2"}}, "node_modules/make-dir": {"version": "3.1.0", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/make-error": {"version": "1.3.6", "dev": true, "license": "ISC"}, "node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/map-age-cleaner": {"version": "0.1.3", "license": "MIT", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/map-cache": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/math-random": {"version": "1.0.4", "license": "MIT"}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mem": {"version": "4.3.0", "license": "MIT", "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/memoizee": {"version": "0.4.17", "license": "ISC", "dependencies": {"d": "^1.0.2", "es5-ext": "^0.10.64", "es6-weak-map": "^2.0.3", "event-emitter": "^0.3.5", "is-promise": "^2.2.2", "lru-queue": "^0.1.0", "next-tick": "^1.1.0", "timers-ext": "^0.1.7"}, "engines": {"node": ">=0.12"}}, "node_modules/merge": {"version": "1.2.1", "license": "MIT"}, "node_modules/merge-descriptors": {"version": "1.0.1", "license": "MIT"}, "node_modules/merge-graphql-schemas": {"version": "1.7.6", "license": "MIT", "dependencies": {"@graphql-toolkit/file-loading": "0.9.0", "@graphql-toolkit/schema-merging": "0.9.0", "tslib": "1.10.0"}, "peerDependencies": {"graphql": "^0.11.7 || ^0.13.0 || ^14.0.2"}}, "node_modules/merge-graphql-schemas/node_modules/tslib": {"version": "1.10.0", "license": "Apache-2.0"}, "node_modules/merge-stream": {"version": "1.0.1", "license": "MIT", "dependencies": {"readable-stream": "^2.0.1"}}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "2.3.11", "license": "MIT", "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/micromatch/node_modules/arr-diff": {"version": "2.0.0", "license": "MIT", "dependencies": {"arr-flatten": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/micromatch/node_modules/array-unique": {"version": "0.2.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/micromatch/node_modules/braces": {"version": "1.8.5", "license": "MIT", "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/micromatch/node_modules/normalize-path": {"version": "2.1.1", "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "2.9.0", "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/minipass/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/minizlib": {"version": "1.3.3", "license": "MIT", "dependencies": {"minipass": "^2.9.0"}}, "node_modules/mixin-deep": {"version": "1.3.2", "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-deep/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/module-details-from-path": {"version": "1.0.3", "license": "MIT"}, "node_modules/moment": {"version": "2.30.1", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.48", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/multer": {"version": "1.4.2", "license": "MIT", "dependencies": {"append-field": "^1.0.0", "busboy": "^0.2.11", "concat-stream": "^1.5.2", "mkdirp": "^0.5.1", "object-assign": "^4.1.1", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/multimatch": {"version": "4.0.0", "license": "MIT", "optional": true, "dependencies": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/multimatch/node_modules/@types/minimatch": {"version": "3.0.5", "license": "MIT", "optional": true}, "node_modules/multimatch/node_modules/arrify": {"version": "2.0.1", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/nanomatch": {"version": "1.2.13", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/natural-compare": {"version": "1.4.0", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/nest-raven": {"version": "5.0.0", "license": "MIT", "dependencies": {"@nestjs/graphql": "^6.2.4", "@sentry/minimal": "^5.2.0", "@sentry/node": "^5.3.0", "apollo-server-express": "^2.6.3", "graphql": "^14.3.1"}}, "node_modules/nest-raven/node_modules/@sentry/core": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/hub": "5.30.0", "@sentry/minimal": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/nest-raven/node_modules/@sentry/node": {"version": "5.30.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sentry/core": "5.30.0", "@sentry/hub": "5.30.0", "@sentry/tracing": "5.30.0", "@sentry/types": "5.30.0", "@sentry/utils": "5.30.0", "cookie": "^0.4.1", "https-proxy-agent": "^5.0.0", "lru_map": "^0.3.3", "tslib": "^1.9.3"}, "engines": {"node": ">=6"}}, "node_modules/nest-raven/node_modules/cookie": {"version": "0.4.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/next-tick": {"version": "1.1.0", "license": "ISC"}, "node_modules/nice-try": {"version": "1.0.5", "license": "MIT"}, "node_modules/nocache": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/node-addon-api": {"version": "5.1.0", "license": "MIT"}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-fetch/node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/node-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/node-fetch/node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "node_modules/node-notifier": {"version": "5.4.5", "license": "MIT", "dependencies": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}}, "node_modules/nodemon": {"version": "1.19.4", "hasInstallScript": true, "license": "MIT", "dependencies": {"chokidar": "^2.1.8", "debug": "^3.2.6", "ignore-by-default": "^1.0.1", "minimatch": "^3.0.4", "pstree.remy": "^1.1.7", "semver": "^5.7.1", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.2", "update-notifier": "^2.5.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=4"}}, "node_modules/nodemon/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/nopt": {"version": "5.0.0", "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "2.0.2", "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npmlog": {"version": "5.0.1", "license": "ISC", "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}}, "node_modules/number-is-nan": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/nwsapi": {"version": "2.2.20", "license": "MIT"}, "node_modules/oauth-sign": {"version": "0.9.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy": {"version": "0.1.0", "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object-path": {"version": "0.11.8", "license": "MIT", "engines": {"node": ">= 10.12.0"}}, "node_modules/object-visit": {"version": "1.0.1", "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.1.8", "license": "MIT", "dependencies": {"array.prototype.reduce": "^1.0.6", "call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "gopd": "^1.0.1", "safe-array-concat": "^1.1.2"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.omit": {"version": "2.0.1", "license": "MIT", "dependencies": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/obuf": {"version": "1.1.2", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opentracing": {"version": "0.14.7", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/optional": {"version": "0.1.4", "license": "MIT"}, "node_modules/optionator": {"version": "0.8.3", "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/os-homedir": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/os-locale": {"version": "3.1.0", "license": "MIT", "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/own-keys": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-defer": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-finally": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-is-promise": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "1.3.0", "license": "MIT", "dependencies": {"p-try": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/p-locate": {"version": "2.0.0", "license": "MIT", "dependencies": {"p-limit": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/p-try": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/package-json": {"version": "4.0.1", "license": "MIT", "dependencies": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "engines": {"node": ">=4"}}, "node_modules/parse-glob": {"version": "3.0.4", "license": "MIT", "dependencies": {"glob-base": "^0.3.0", "is-dotfile": "^1.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-json": {"version": "2.2.0", "license": "MIT", "dependencies": {"error-ex": "^1.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/parse5-htmlparser2-tree-adapter": {"version": "6.0.1", "license": "MIT", "dependencies": {"parse5": "^6.0.1"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascalcase": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/passport": {"version": "0.4.1", "license": "MIT", "dependencies": {"passport-strategy": "1.x.x", "pause": "0.0.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/passport-http-bearer": {"version": "1.0.1", "dependencies": {"passport-strategy": "1.x.x"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/passport-jwt": {"version": "4.0.1", "license": "MIT", "dependencies": {"jsonwebtoken": "^9.0.0", "passport-strategy": "^1.0.0"}}, "node_modules/passport-jwt/node_modules/jsonwebtoken": {"version": "9.0.2", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/passport-jwt/node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/passport-strategy": {"version": "1.0.0", "engines": {"node": ">= 0.4.0"}}, "node_modules/path-dirname": {"version": "1.0.2", "license": "MIT"}, "node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-inside": {"version": "1.0.2", "license": "(WTFPL OR MIT)"}, "node_modules/path-key": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "3.2.0", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pause": {"version": "0.0.1"}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/pg": {"version": "8.15.5", "license": "MIT", "dependencies": {"pg-connection-string": "^2.8.5", "pg-pool": "^3.9.5", "pg-protocol": "^1.9.5", "pg-types": "^2.1.0", "pgpass": "1.x"}, "engines": {"node": ">= 8.0.0"}, "optionalDependencies": {"pg-cloudflare": "^1.2.5"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}}, "node_modules/pg-cloudflare": {"version": "1.2.5", "license": "MIT", "optional": true}, "node_modules/pg-connection-string": {"version": "2.8.5", "license": "MIT"}, "node_modules/pg-int8": {"version": "1.0.1", "license": "ISC", "engines": {"node": ">=4.0.0"}}, "node_modules/pg-numeric": {"version": "1.0.2", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/pg-pool": {"version": "3.9.5", "license": "MIT", "peerDependencies": {"pg": ">=8.0"}}, "node_modules/pg-protocol": {"version": "1.9.5", "license": "MIT"}, "node_modules/pg-types": {"version": "2.2.0", "license": "MIT", "dependencies": {"pg-int8": "1.0.1", "postgres-array": "~2.0.0", "postgres-bytea": "~1.0.0", "postgres-date": "~1.0.4", "postgres-interval": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/pgpass": {"version": "1.0.5", "license": "MIT", "dependencies": {"split2": "^4.1.0"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie": {"version": "2.0.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-dir": {"version": "2.0.0", "license": "MIT", "dependencies": {"find-up": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/pn": {"version": "1.1.0", "license": "MIT"}, "node_modules/posix-character-classes": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postgres-array": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/postgres-bytea": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postgres-date": {"version": "1.0.7", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postgres-interval": {"version": "1.2.0", "license": "MIT", "dependencies": {"xtend": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/postgres-range": {"version": "1.1.4", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/prepend-http": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/preserve": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/prettier": {"version": "1.19.1", "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=4"}}, "node_modules/pretty-format": {"version": "23.6.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0", "ansi-styles": "^3.2.0"}}, "node_modules/pretty-format/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/private": {"version": "0.1.8", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/process": {"version": "0.10.1", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/prompts": {"version": "0.1.14", "dependencies": {"kleur": "^2.0.1", "sisteransi": "^0.1.1"}, "engines": {"node": ">= 6"}}, "node_modules/protobufjs": {"version": "7.1.1", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/pseudomap": {"version": "1.0.2", "license": "ISC"}, "node_modules/psl": {"version": "1.15.0", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/pstree.remy": {"version": "1.1.8", "license": "MIT"}, "node_modules/pump": {"version": "3.0.2", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.13.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystring": {"version": "0.2.0", "engines": {"node": ">=0.4.x"}}, "node_modules/querystringify": {"version": "2.2.0", "license": "MIT"}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/randomatic": {"version": "3.1.1", "license": "MIT", "dependencies": {"is-number": "^4.0.0", "kind-of": "^6.0.0", "math-random": "^1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/randomatic/node_modules/is-number": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/randomatic/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.4.0", "license": "MIT", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.2", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/http-errors": {"version": "1.7.2", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body/node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/react-is": {"version": "16.13.1", "license": "MIT", "optional": true}, "node_modules/read-pkg": {"version": "1.1.0", "license": "MIT", "dependencies": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up": {"version": "1.0.1", "license": "MIT", "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "1.1.2", "license": "MIT", "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/path-exists": {"version": "2.1.0", "license": "MIT", "dependencies": {"pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg/node_modules/path-type": {"version": "1.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/readdirp": {"version": "2.2.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/readdirp/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/readdirp/node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/readdirp/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/realpath-native": {"version": "1.1.0", "license": "MIT", "dependencies": {"util.promisify": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/referrer-policy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/reflect-metadata": {"version": "0.1.14", "license": "Apache-2.0"}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regenerator-runtime": {"version": "0.11.1", "license": "MIT"}, "node_modules/regex-cache": {"version": "0.4.4", "license": "MIT", "dependencies": {"is-equal-shallow": "^0.1.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not": {"version": "1.0.2", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/registry-auth-token": {"version": "3.4.0", "license": "MIT", "dependencies": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "node_modules/registry-url": {"version": "3.1.0", "license": "MIT", "dependencies": {"rc": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "license": "ISC"}, "node_modules/repeat-element": {"version": "1.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/repeating": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-finite": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/request": {"version": "2.88.2", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request-ip": {"version": "2.2.0", "license": "MIT", "dependencies": {"is_js": "^0.9.0"}}, "node_modules/request-promise-core": {"version": "1.1.4", "license": "ISC", "dependencies": {"lodash": "^4.17.19"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request-promise-native": {"version": "1.0.9", "license": "ISC", "dependencies": {"request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "engines": {"node": ">=0.12.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request/node_modules/form-data": {"version": "2.3.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/request/node_modules/qs": {"version": "6.5.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-in-the-middle": {"version": "7.5.2", "license": "MIT", "dependencies": {"debug": "^4.3.5", "module-details-from-path": "^1.0.3", "resolve": "^1.22.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/require-in-the-middle/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/require-main-filename": {"version": "1.0.1", "license": "ISC"}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "2.0.0", "license": "MIT", "dependencies": {"resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/resolve-from": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-url": {"version": "0.2.1", "license": "MIT"}, "node_modules/ret": {"version": "0.1.15", "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/retry": {"version": "0.13.1", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.1.0", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/rsvp": {"version": "3.6.2", "license": "MIT", "engines": {"node": "0.12.* || 4.* || 6.* || >= 7.*"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "6.6.7", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-array-concat/node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-push-apply/node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/safe-regex": {"version": "1.1.0", "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sane": {"version": "2.5.2", "license": "MIT", "dependencies": {"anymatch": "^2.0.0", "capture-exit": "^1.2.0", "exec-sh": "^0.2.0", "fb-watchman": "^2.0.0", "micromatch": "^3.1.4", "minimist": "^1.1.1", "walker": "~1.0.5", "watch": "~0.18.0"}, "bin": {"sane": "src/cli.js"}, "engines": {"node": ">=0.6.0"}, "optionalDependencies": {"fsevents": "^1.2.3"}}, "node_modules/sane/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/sane/node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sax": {"version": "1.4.1", "license": "ISC"}, "node_modules/saxes": {"version": "5.0.1", "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/semver-diff": {"version": "2.1.0", "license": "MIT", "dependencies": {"semver": "^5.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/send": {"version": "0.17.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.7.2", "mime": "1.6.0", "ms": "2.1.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1", "statuses": "~1.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/destroy": {"version": "1.0.4", "license": "MIT"}, "node_modules/send/node_modules/http-errors": {"version": "1.7.3", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/send/node_modules/ms": {"version": "2.1.1", "license": "MIT"}, "node_modules/send/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/serve-static": {"version": "1.14.1", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.17.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-value": {"version": "2.0.1", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/setprototypeof": {"version": "1.1.1", "license": "ISC"}, "node_modules/sha.js": {"version": "2.4.11", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/shebang-command": {"version": "1.2.0", "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/shellwords": {"version": "0.1.1", "license": "MIT"}, "node_modules/shimmer": {"version": "1.2.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/short-uuid": {"version": "3.1.1", "license": "MIT", "dependencies": {"any-base": "^1.1.0", "uuid": "^3.3.2"}}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/sisteransi": {"version": "0.1.1", "license": "MIT"}, "node_modules/slash": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon": {"version": "0.8.2", "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "license": "MIT"}, "node_modules/spdx-correct": {"version": "3.2.0", "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "license": "CC0-1.0"}, "node_modules/split-string": {"version": "3.1.0", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split-string/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split-string/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split2": {"version": "4.2.0", "license": "ISC", "engines": {"node": ">= 10.x"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sshpk": {"version": "1.18.0", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stack-utils": {"version": "1.0.5", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/static-extend": {"version": "0.1.2", "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/stealthy-require": {"version": "1.1.1", "license": "ISC", "engines": {"node": ">=0.10.0"}}, "node_modules/streamsearch": {"version": "0.1.2", "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/string-length": {"version": "2.0.0", "license": "MIT", "dependencies": {"astral-regex": "^1.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/string-length/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/string-length/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/string-template": {"version": "0.2.1"}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "2.0.0", "license": "MIT", "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-eof": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/subscriptions-transport-ws": {"version": "0.9.19", "license": "MIT", "dependencies": {"backo2": "^1.0.2", "eventemitter3": "^3.1.0", "iterall": "^1.2.1", "symbol-observable": "^1.0.4", "ws": "^5.2.0 || ^6.0.0 || ^7.0.0"}, "peerDependencies": {"graphql": ">=0.10.0"}}, "node_modules/superagent": {"version": "3.8.3", "dev": true, "license": "MIT", "dependencies": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "engines": {"node": ">= 4.0"}}, "node_modules/superagent/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/superagent/node_modules/form-data": {"version": "2.5.3", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/superagent/node_modules/qs": {"version": "6.14.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/supertest": {"version": "3.4.2", "dev": true, "license": "MIT", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "engines": {"node": ">=6.0.0"}}, "node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/symbol-observable": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/symbol-tree": {"version": "3.2.4", "license": "MIT"}, "node_modules/tar": {"version": "4.4.19", "license": "ISC", "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "engines": {"node": ">=4.5"}}, "node_modules/tar/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/term-size": {"version": "1.2.0", "license": "MIT", "dependencies": {"execa": "^0.7.0"}, "engines": {"node": ">=4"}}, "node_modules/term-size/node_modules/cross-spawn": {"version": "5.1.0", "license": "MIT", "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/term-size/node_modules/execa": {"version": "0.7.0", "license": "MIT", "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/term-size/node_modules/lru-cache": {"version": "4.1.5", "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/term-size/node_modules/yallist": {"version": "2.1.2", "license": "ISC"}, "node_modules/test-exclude": {"version": "4.2.3", "license": "ISC", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "object-assign": "^4.1.0", "read-pkg-up": "^1.0.1", "require-main-filename": "^1.0.1"}}, "node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/thriftrw": {"version": "3.11.4", "dependencies": {"bufrw": "^1.2.1", "error": "7.0.2", "long": "^2.4.0"}, "bin": {"thrift2json": "thrift2json.js"}, "engines": {"node": ">= 0.10.x"}}, "node_modules/thriftrw/node_modules/long": {"version": "2.4.0", "license": "Apache-2.0", "engines": {"node": ">=0.6"}}, "node_modules/throat": {"version": "4.1.0", "license": "MIT"}, "node_modules/timed-out": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/timers-ext": {"version": "0.1.8", "license": "ISC", "dependencies": {"es5-ext": "^0.10.64", "next-tick": "^1.1.0"}, "engines": {"node": ">=0.12"}}, "node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-fast-properties": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path": {"version": "0.3.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/touch": {"version": "3.1.1", "license": "ISC", "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tough-cookie": {"version": "2.5.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tr46": {"version": "1.0.1", "license": "MIT", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/trim-right": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ts-invariant": {"version": "0.4.4", "license": "MIT", "dependencies": {"tslib": "^1.9.3"}}, "node_modules/ts-jest": {"version": "23.10.5", "dev": true, "license": "MIT", "dependencies": {"bs-logger": "0.x", "buffer-from": "1.x", "fast-json-stable-stringify": "2.x", "json5": "2.x", "make-error": "1.x", "mkdirp": "0.x", "resolve": "1.x", "semver": "^5.5", "yargs-parser": "10.x"}, "bin": {"ts-jest": "cli.js"}, "engines": {"node": ">= 6"}, "peerDependencies": {"jest": ">=22 <24"}}, "node_modules/ts-morph": {"version": "5.0.0", "license": "MIT", "optional": true, "dependencies": {"@dsherret/to-absolute-glob": "^2.0.2", "@ts-morph/common": "~0.1.0", "code-block-writer": "^10.0.0"}}, "node_modules/ts-node": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"arrify": "^1.0.0", "buffer-from": "^1.1.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.6", "yn": "^2.0.0"}, "bin": {"ts-node": "dist/bin.js"}, "engines": {"node": ">=4.2.0"}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/tsconfig-paths/node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/tslint": {"version": "5.12.1", "dev": true, "license": "Apache-2.0", "dependencies": {"babel-code-frame": "^6.22.0", "builtin-modules": "^1.1.1", "chalk": "^2.3.0", "commander": "^2.12.1", "diff": "^3.2.0", "glob": "^7.1.1", "js-yaml": "^3.7.0", "minimatch": "^3.0.4", "resolve": "^1.3.2", "semver": "^5.3.0", "tslib": "^1.8.0", "tsutils": "^2.27.2"}, "bin": {"tslint": "bin/tslint"}, "engines": {"node": ">=4.8.0"}, "peerDependencies": {"typescript": ">=2.1.0 || >=2.1.0-dev || >=2.2.0-dev || >=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev"}}, "node_modules/tsutils": {"version": "2.29.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "peerDependencies": {"typescript": ">=2.1.0 || >=2.1.0-dev || >=2.2.0-dev || >=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >= 3.0.0-dev || >= 3.1.0-dev"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "license": "Unlicense"}, "node_modules/type": {"version": "2.7.3", "license": "ISC"}, "node_modules/type-check": {"version": "0.3.2", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-graphql": {"version": "0.17.6", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"@types/glob": "^7.1.1", "@types/node": "*", "@types/semver": "^6.0.1", "class-validator": ">=0.9.1", "glob": "^7.1.4", "graphql-query-complexity": "^0.3.0", "graphql-subscriptions": "^1.1.0", "semver": "^6.2.0", "tslib": "^1.10.0"}, "engines": {"node": ">= 8.x"}, "peerDependencies": {"@types/graphql": "^14.0.7", "graphql": "^14.1.1"}}, "node_modules/type-graphql/node_modules/@types/node": {"version": "12.20.55", "license": "MIT", "optional": true}, "node_modules/type-graphql/node_modules/class-validator": {"version": "0.14.1", "license": "MIT", "optional": true, "dependencies": {"@types/validator": "^13.11.8", "libphonenumber-js": "^1.10.53", "validator": "^13.9.0"}}, "node_modules/type-graphql/node_modules/semver": {"version": "6.3.1", "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/type-graphql/node_modules/validator": {"version": "13.15.0", "license": "MIT", "optional": true, "engines": {"node": ">= 0.10"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typedarray": {"version": "0.0.6", "license": "MIT"}, "node_modules/typeorm": {"version": "0.2.45", "license": "MIT", "dependencies": {"@sqltools/formatter": "^1.2.2", "app-root-path": "^3.0.0", "buffer": "^6.0.3", "chalk": "^4.1.0", "cli-highlight": "^2.1.11", "debug": "^4.3.1", "dotenv": "^8.2.0", "glob": "^7.1.6", "js-yaml": "^4.0.0", "mkdirp": "^1.0.4", "reflect-metadata": "^0.1.13", "sha.js": "^2.4.11", "tslib": "^2.1.0", "uuid": "^8.3.2", "xml2js": "^0.4.23", "yargs": "^17.0.1", "zen-observable-ts": "^1.0.0"}, "bin": {"typeorm": "cli.js"}, "funding": {"url": "https://opencollective.com/typeorm"}, "peerDependencies": {"@sap/hana-client": "^2.11.14", "better-sqlite3": "^7.1.2", "hdb-pool": "^0.1.6", "ioredis": "^4.28.3", "mongodb": "^3.6.0", "mssql": "^6.3.1", "mysql2": "^2.2.5", "oracledb": "^5.1.0", "pg": "^8.5.1", "pg-native": "^3.0.0", "pg-query-stream": "^4.0.0", "redis": "^3.1.1", "sql.js": "^1.4.0", "sqlite3": "^5.0.2", "typeorm-aurora-data-api-driver": "^2.0.0"}, "peerDependenciesMeta": {"@sap/hana-client": {"optional": true}, "better-sqlite3": {"optional": true}, "hdb-pool": {"optional": true}, "ioredis": {"optional": true}, "mongodb": {"optional": true}, "mssql": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-native": {"optional": true}, "pg-query-stream": {"optional": true}, "redis": {"optional": true}, "sql.js": {"optional": true}, "sqlite3": {"optional": true}, "typeorm-aurora-data-api-driver": {"optional": true}}}, "node_modules/typeorm/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/typeorm/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/typeorm/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/typeorm/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/typeorm/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/typeorm/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/typeorm/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/typeorm/node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/typeorm/node_modules/dotenv": {"version": "8.6.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/typeorm/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/typeorm/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/typeorm/node_modules/mkdirp": {"version": "1.0.4", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/typeorm/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/typeorm/node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/typeorm/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/typeorm/node_modules/xml2js": {"version": "0.4.23", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/typeorm/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/typeorm/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/typescript": {"version": "4.9.5", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/uglify-js": {"version": "3.19.3", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unc-path-regex": {"version": "0.1.2", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/undefsafe": {"version": "2.0.5", "license": "MIT"}, "node_modules/undici-types": {"version": "6.21.0", "license": "MIT"}, "node_modules/union-value": {"version": "1.0.1", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unique-string": {"version": "1.0.0", "license": "MIT", "dependencies": {"crypto-random-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/universalify": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unixify": {"version": "1.0.0", "license": "MIT", "dependencies": {"normalize-path": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unixify/node_modules/normalize-path": {"version": "2.1.1", "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unset-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unzip-response": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/upath": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/update-notifier": {"version": "2.5.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-ci": "^1.0.10", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "license": "MIT"}, "node_modules/url": {"version": "0.10.3", "license": "MIT", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/url-parse": {"version": "1.5.10", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/url-parse-lax": {"version": "1.0.0", "license": "MIT", "dependencies": {"prepend-http": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/url/node_modules/punycode": {"version": "1.3.2", "license": "MIT"}, "node_modules/use": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/util": {"version": "0.12.5", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/util.promisify": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "for-each": "^0.3.3", "get-intrinsic": "^1.2.6", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "object.getownpropertydescriptors": "^2.1.8", "safe-array-concat": "^1.1.3"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.4.0", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/validator": {"version": "10.4.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/verror/node_modules/extsprintf": {"version": "1.4.1", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/w3c-hr-time": {"version": "1.0.2", "license": "MIT", "dependencies": {"browser-process-hrtime": "^1.0.0"}}, "node_modules/w3c-xmlserializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/watch": {"version": "0.18.0", "license": "Apache-2.0", "dependencies": {"exec-sh": "^0.2.0", "minimist": "^1.2.0"}, "bin": {"watch": "cli.js"}, "engines": {"node": ">=0.1.95"}}, "node_modules/webidl-conversions": {"version": "4.0.2", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-encoding": {"version": "1.0.5", "license": "MIT", "dependencies": {"iconv-lite": "0.4.24"}}, "node_modules/whatwg-mimetype": {"version": "2.3.0", "license": "MIT"}, "node_modules/whatwg-url": {"version": "8.7.0", "license": "MIT", "dependencies": {"lodash": "^4.7.0", "tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "engines": {"node": ">=10"}}, "node_modules/whatwg-url/node_modules/tr46": {"version": "2.1.0", "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/whatwg-url/node_modules/webidl-conversions": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type/node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-module": {"version": "2.0.1", "license": "ISC"}, "node_modules/which-typed-array": {"version": "1.1.19", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wide-align": {"version": "1.1.5", "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/widest-line": {"version": "2.0.1", "license": "MIT", "dependencies": {"string-width": "^2.1.1"}, "engines": {"node": ">=4"}}, "node_modules/widest-line/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/widest-line/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/widest-line/node_modules/string-width": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/widest-line/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/word-wrap": {"version": "1.2.5", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wordwrap": {"version": "1.0.0", "license": "MIT"}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "2.4.3", "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/x-xss-protection": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/xdg-basedir": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/xml-name-validator": {"version": "3.0.0", "license": "Apache-2.0"}, "node_modules/xml2js": {"version": "0.6.2", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/xmlchars": {"version": "2.2.0", "license": "MIT"}, "node_modules/xorshift": {"version": "1.2.0", "license": "MIT"}, "node_modules/xss": {"version": "1.0.15", "license": "MIT", "dependencies": {"commander": "^2.20.3", "cssfilter": "0.0.10"}, "bin": {"xss": "bin/xss"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yargs": {"version": "11.1.1", "license": "MIT", "dependencies": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^3.1.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^9.0.2"}}, "node_modules/yargs-parser": {"version": "10.1.0", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^4.1.0"}}, "node_modules/yargs/node_modules/ansi-regex": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/yargs/node_modules/get-caller-file": {"version": "1.0.3", "license": "ISC"}, "node_modules/yargs/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/yargs/node_modules/string-width": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yargs/node_modules/strip-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yargs/node_modules/y18n": {"version": "3.2.2", "license": "ISC"}, "node_modules/yargs/node_modules/yargs-parser": {"version": "9.0.2", "license": "ISC", "dependencies": {"camelcase": "^4.1.0"}}, "node_modules/yn": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/zen-observable": {"version": "0.8.15", "license": "MIT"}, "node_modules/zen-observable-ts": {"version": "1.1.0", "license": "MIT", "dependencies": {"@types/zen-observable": "0.8.3", "zen-observable": "0.8.15"}}}}
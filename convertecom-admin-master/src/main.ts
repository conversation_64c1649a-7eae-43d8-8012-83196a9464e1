import { config } from 'dotenv';
config();

import { tracer } from './tracing';
tracer();

import { NestFactory } from '@nestjs/core';
import * as Sentry from '@sentry/node';
import { RavenInterceptor } from 'nest-raven';
import { HttpException, ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  if (process.env.SENTRY_DSN) {
    Sentry.init({ dsn: process.env.SENTRY_DSN });
    app.use(Sentry.Handlers.requestHandler());
    app.useGlobalInterceptors(new RavenInterceptor({
      filters: [
        { type: HttpException, filter: (exception: HttpException) => 500 > exception.getStatus() }
      ],
    }));
  }

  // app.useGlobalGuards(new RolesGuard());
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.enableCors();

  await app.listen(process.env.PORT ? parseInt(process.env.PORT, 10) : 3000);
}
bootstrap();
